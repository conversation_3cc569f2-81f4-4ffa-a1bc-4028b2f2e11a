KVWF      mandelbrot_wave   fractal0   Waveform modulated by Mandelbrot-like iterations   high   KarmaViz Generator   1.0%  float compute_waveform_at_x(float x_coord) {
    if (!waveform_enabled || waveform_length <= 0) {
        return 0.0;
    }
    
    float norm_x = clamp(x_coord, 0.0, 1.0);
    float sample = texture(waveform_data, vec2(norm_x, 0.5)).r;
    
    // Simple fractal-like iteration
    float z = norm_x * 2.0 - 1.0;
    float c = sample * 0.5;
    for (int i = 0; i < 5; i++) {
        z = z * z + c;
        if (abs(z) > 2.0) break;
    }
    
    float fractal_mod = clamp(abs(z) / 2.0, 0.0, 1.0);
    return sample * fractal_mod * waveform_scale;
}