KVWF   	   julia_set   fractal$   Julia set fractal modulated by audio   high   KarmaViz Generator   1.0=  float compute_waveform_intensity_at_xy(float x_coord, float y_coord) {
    if (!waveform_enabled || waveform_length <= 0) return 0.0;
    
    // Julia set parameters modulated by audio
    float sample = texture(waveform_data, vec2(0.5, 0.5)).r;
    vec2 c = vec2(-0.7 + sample * 0.2, 0.27015 + sample * 0.1);
    
    // Map screen coordinates to complex plane
    vec2 z = vec2(x_coord - 0.5, y_coord - 0.5) * 3.0;
    
    int iterations = 0;
    for (int i = 0; i < 32; i++) {
        if (dot(z, z) > 4.0) break;
        
        // z = z^2 + c
        float temp = z.x * z.x - z.y * z.y + c.x;
        z.y = 2.0 * z.x * z.y + c.y;
        z.x = temp;
        
        iterations++;
    }
    
    float intensity = float(iterations) / 32.0;
    return intensity;
}
float compute_waveform_at_x(float x_coord) { return 0.0; }