KVWF      dragon_curve   fractal   Dragon curve fractal pattern   high   KarmaViz Advanced Generator   1.0[  float compute_waveform_at_x(float x_coord) {
    if (!waveform_enabled || waveform_length <= 0) {
        return 0.0;
    }
    
    float norm_x = clamp(x_coord, 0.0, 1.0);
    float sample = texture(waveform_data, vec2(norm_x, 0.5)).r;
    
    // Dragon curve approximation
    float dragon = 0.0;
    float x = norm_x * 4.0 - 2.0;
    
    for (int i = 0; i < 8; i++) {
        float scale = pow(0.7, float(i));
        float offset = sin(float(i) * 2.1) * scale;
        dragon += sin(x / scale + offset + time * 0.5) * scale;
    }
    
    return sample * (1.0 + dragon * 0.3) * waveform_scale;
}