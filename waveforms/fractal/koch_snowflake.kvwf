KVWF      koch_snowflake   fractal   Koch snowflake edge pattern   high   KarmaViz Advanced Generator   1.0.  float compute_waveform_at_x(float x_coord) {
    if (!waveform_enabled || waveform_length <= 0) {
        return 0.0;
    }
    
    float norm_x = clamp(x_coord, 0.0, 1.0);
    float sample = texture(waveform_data, vec2(norm_x, 0.5)).r;
    
    // Koch snowflake edge approximation
    float koch = 0.0;
    float scale = 1.0;
    
    for (int i = 0; i < 6; i++) {
        float segment = fract(norm_x * scale);
        if (segment < 0.33) {
            koch += sin(segment * 9.42477) * (1.0 / scale);
        } else if (segment < 0.67) {
            koch += sin((segment - 0.33) * 9.42477 + 2.094) * (1.0 / scale);
        } else {
            koch += sin((segment - 0.67) * 9.42477) * (1.0 / scale);
        }
        scale *= 3.0;
    }
    
    return sample * (1.0 + koch * abs(sample)) * waveform_scale;
}