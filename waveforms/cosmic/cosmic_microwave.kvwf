KVWF      cosmic_microwave   cosmic%   Cosmic microwave background radiation   medium   KarmaViz Advanced Generator   1.0
  float compute_waveform_at_x(float x_coord) {
    if (!waveform_enabled || waveform_length <= 0) {
        return 0.0;
    }
    
    float norm_x = clamp(x_coord, 0.0, 1.0);
    float sample = texture(waveform_data, vec2(norm_x, 0.5)).r;
    
    // CMB temperature fluctuations
    float cmb_base = 2.725; // Kelvin
    float fluctuation = 0.0;
    
    // Multiple scales of fluctuation
    for (int i = 1; i <= 8; i++) {
        float scale = pow(2.0, float(i));
        float amplitude = 1.0 / scale;
        fluctuation += sin(norm_x * scale * 6.28318 + time * 0.1) * amplitude;
    }
    
    // Modulate by audio
    float cmb_signal = fluctuation * abs(sample) * 0.001; // Microkelvin scale
    
    return cmb_signal * 1000.0 * waveform_scale; // Scale up for visibility
}