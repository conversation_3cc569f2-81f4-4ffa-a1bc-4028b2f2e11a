KVWF      black_hole_accretion   cosmic"   Black hole accretion disk dynamics   high   KarmaViz Advanced Generator   1.0)  float compute_waveform_at_x(float x_coord) {
    if (!waveform_enabled || waveform_length <= 0) {
        return 0.0;
    }
    
    float norm_x = clamp(x_coord, 0.0, 1.0);
    float sample = texture(waveform_data, vec2(norm_x, 0.5)).r;
    
    // Event horizon at center
    float event_horizon = 0.5;
    float distance_to_bh = abs(norm_x - event_horizon);
    
    // Accretion disk
    float disk_radius = 0.3;
    float in_disk = step(distance_to_bh, disk_radius);
    
    // Orbital velocity (faster closer to black hole)
    float orbital_speed = 1.0 / (distance_to_bh + 0.1);
    float orbital_phase = time * orbital_speed;
    
    // Plasma heating
    float plasma_temp = abs(sample) / (distance_to_bh + 0.1);
    
    // Gravitational redshift
    float redshift = 1.0 - 1.0 / sqrt(1.0 + distance_to_bh);
    
    // Jets from poles
    float jet_intensity = exp(-pow(distance_to_bh * 10.0, 2.0)) * abs(sample);
    
    float accretion = in_disk * plasma_temp * redshift + jet_intensity;
    return accretion * sin(orbital_phase) * waveform_scale;
}