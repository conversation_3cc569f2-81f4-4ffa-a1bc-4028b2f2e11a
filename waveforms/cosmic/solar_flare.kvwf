KVWF      solar_flare   cosmic    Solar flare energy burst pattern   high   KarmaViz Advanced Generator   1.0  float compute_waveform_at_x(float x_coord) {
    if (!waveform_enabled || waveform_length <= 0) {
        return 0.0;
    }
    
    float norm_x = clamp(x_coord, 0.0, 1.0);
    float sample = texture(waveform_data, vec2(norm_x, 0.5)).r;
    
    // Solar flare eruption
    float flare_center = 0.3 + sin(time * 0.5) * 0.2;
    float flare_intensity = abs(sample);
    float distance_to_flare = abs(norm_x - flare_center);
    
    // Magnetic field lines
    float magnetic_field = sin(norm_x * 15.708 + time * 3.0) * 0.2;
    
    // Plasma ejection
    float plasma = exp(-distance_to_flare * 5.0) * flare_intensity;
    plasma += exp(-distance_to_flare * 2.0) * flare_intensity * 0.3;
    
    return (plasma + magnetic_field * flare_intensity) * waveform_scale;
}