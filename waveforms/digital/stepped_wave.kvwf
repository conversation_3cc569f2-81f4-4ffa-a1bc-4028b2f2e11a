KVWF      stepped_wave   digital.   Stepped digital waveform with quantized levels   medium   KarmaViz Generator   1.0J  float compute_waveform_at_x(float x_coord) {
    if (!waveform_enabled || waveform_length <= 0) {
        return 0.0;
    }
    
    float norm_x = clamp(x_coord, 0.0, 1.0);
    float sample = texture(waveform_data, vec2(norm_x, 0.5)).r;
    
    // Create stepped levels
    return floor(sample * 16.0) / 16.0 * waveform_scale;
}