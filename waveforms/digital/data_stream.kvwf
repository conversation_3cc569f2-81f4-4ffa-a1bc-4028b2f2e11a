KVWF      data_stream   digital    Binary data stream visualization   medium   KarmaViz Advanced Generator   1.0  float compute_waveform_at_x(float x_coord) {
    if (!waveform_enabled || waveform_length <= 0) {
        return 0.0;
    }
    
    float norm_x = clamp(x_coord, 0.0, 1.0);
    float sample = texture(waveform_data, vec2(norm_x, 0.9)).r;
    
    // Data stream effect
    float stream_pos = norm_x * 32.0 + time * 8.0;
    float bit_value = step(0.5, fract(sin(floor(stream_pos) * 12.9898) * 43758.5453));
    
    // Modulate by audio
    float intensity = abs(sample);
    return (bit_value * 2.0 - 1.0) * intensity * waveform_scale;
}