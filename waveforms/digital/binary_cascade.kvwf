KVWF      Binary Cascade   digital   Falling streams of binary code   intermediate   KarmaViz Advanced Collection   1.0  // Binary Cascade
float compute_waveform_intensity_at_xy(float x_coord, float y_coord) {
    if (!waveform_enabled || waveform_length <= 0) return 0.0;
    float cols = 16.0;
    float col_idx = floor(x_coord * cols);
    float fft = texture(fft_data, vec2(col_idx / cols, 0.5)).r;
    float cascade_speed = 1.0 + fft * 3.0;
    float cascade_time = time * cascade_speed + col_idx * 2.0;
    float bit_size = 0.04;
    float bits_per_col = 1.0 / bit_size;
    float bit_idx = floor(y_coord * bits_per_col);
    float bit_y = (bit_idx + 0.5) / bits_per_col;
    float bit_dist = abs(y_coord - bit_y);
    if (bit_dist < bit_size * 0.4) {
        float bit_seed = sin(cascade_time + bit_idx * 3.14159 + col_idx * 7.123) * 43758.5453;
        float bit_value = step(0.5, fract(bit_seed));
        float bit_age = fract(cascade_time * 0.1 + bit_idx * 0.1);
        float bit_brightness = bit_value * (1.0 - bit_age) * (0.5 + fft);
        return bit_brightness;
    }
    return 0.0;
}
float compute_waveform_at_x(float x_coord) { return 0.0; }