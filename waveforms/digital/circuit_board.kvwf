KVWF   
   circuit_board   digital%   Circuit board pattern driven by audio   high   KarmaViz Generator   1.0+  float compute_waveform_intensity_at_xy(float x_coord, float y_coord) {
    if (!waveform_enabled || waveform_length <= 0) return 0.0;
    
    // Create circuit-like grid
    float grid_x = abs(fract(x_coord * 16.0) - 0.5);
    float grid_y = abs(fract(y_coord * 12.0) - 0.5);
    
    float sample = texture(waveform_data, vec2(x_coord, 0.5)).r;
    
    // Circuit traces
    float trace = step(0.45, max(grid_x, grid_y));
    float intensity = abs(sample) * trace;
    
    return intensity;
}
float compute_waveform_at_x(float x_coord) { return 0.0; }