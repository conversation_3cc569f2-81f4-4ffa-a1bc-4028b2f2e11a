KVWF      gameboy_wave   retro   Nintendo Game Boy wave channel   medium   KarmaViz Advanced Generator   1.0G  float compute_waveform_at_x(float x_coord) {
    if (!waveform_enabled || waveform_length <= 0) {
        return 0.0;
    }
    
    float norm_x = clamp(x_coord, 0.0, 1.0);
    float sample = texture(waveform_data, vec2(norm_x, 0.5)).r;
    
    // Game Boy 4-bit wave table
    float wave_pos = fract(sample * 8.0);
    float wave_table[16];
    // Simulate wave table with mathematical function
    float gb_wave = sin(wave_pos * 6.28318) * 0.7 + sin(wave_pos * 12.56636) * 0.3 *glow_radius;
    
    // 4-bit quantization
    return floor(gb_wave * 8.0) / 8.0 * waveform_scale;
}