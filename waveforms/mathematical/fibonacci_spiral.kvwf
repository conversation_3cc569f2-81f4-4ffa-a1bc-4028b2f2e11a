KVWF      fibonacci_spiral   mathematical.   Fibonacci spiral pattern with audio modulation   high   KarmaViz Generator   1.0!  float compute_waveform_intensity_at_xy(float x_coord, float y_coord) {
    if (!waveform_enabled || waveform_length <= 0) return 0.0;
    
    float dx = x_coord - 0.5;
    float dy = y_coord - 0.5;
    float radius = length(vec2(dx, dy)/2);
    float angle = atan(dy, dx);
    
    // Fibonacci spiral approximation
    float spiral_r = 0.1 * exp(angle * 0.306); // Golden ratio approximation
    float dist_to_spiral = abs(radius - spiral_r);
    
    // Sample audio based on spiral position
    float spiral_pos = clamp((angle + 3.14159) / 6.28318, 0.0, 1.0);
    float sample = texture(waveform_data, vec2(spiral_pos, 0.5)).r;
    
    float intensity = exp(-dist_to_spiral * 20.0) * abs(sample);
    return clamp(intensity, 0.0, 1.0);
}
float compute_waveform_at_x(float x_coord) { return 0.0; }