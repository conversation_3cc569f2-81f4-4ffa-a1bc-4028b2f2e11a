KVWF      force_field
   futuristic   Energy force field distortion   high   KarmaViz Advanced Generator   1.0u  float compute_waveform_at_x(float x_coord) {
    if (!waveform_enabled || waveform_length <= 0) {
        return 0.0;
    }
    
    float norm_x = clamp(x_coord, 0.0, 1.0);
    float sample = texture(waveform_data, vec2(norm_x, 0.5)).r;
    
    // Force field distortion
    float field_strength = abs(sample);
    float distortion = sin(norm_x * 20.0 + time * 5.0) * field_strength * 0.3;
    float field_sample = texture(waveform_data, vec2(norm_x + distortion * 0.1, 0.5)).r;
    
    // Energy ripples
    float ripple = sin(norm_x * 15.0 - time * 8.0) * 0.2 + 0.8;
    
    return field_sample * ripple * waveform_scale;
}