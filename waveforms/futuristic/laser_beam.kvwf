KVWF   
   laser_beam
   futuristic   Focused laser beam intensity   medium   KarmaViz Advanced Generator   1.0R  float compute_waveform_at_x(float x_coord) {
    if (!waveform_enabled || waveform_length <= 0) {
        return 0.0;
    }
    
    float norm_x = clamp(x_coord, 0.0, 1.0);
    float sample = texture(waveform_data, vec2(norm_x, 0.5)).r;
    
    // Laser beam focus
    float beam_center = 0.5 + sample * 0.2;
    float beam_width = 0.02 + abs(sample) * 0.01;
    float beam_intensity = exp(-pow(abs(norm_x - beam_center) / beam_width, 2.0));
    
    // Laser flicker
    float flicker = sin(time * 60.0) * 0.1 + 0.9;
    
    return beam_intensity * flicker * abs(sample) * waveform_scale;
}