KVWF      quantum_tunnel   experimental"   Quantum tunneling probability wave   high   KarmaViz Advanced Generator   1.0`  float compute_waveform_at_x(float x_coord) {
    if (!waveform_enabled || waveform_length <= 0) {
        return 0.0;
    }
    
    float norm_x = clamp(x_coord, 0.0, 1.0);
    float sample = texture(waveform_data, vec2(norm_x, 0.5)).r;
    
    // Quantum tunneling barrier
    float barrier_center = 0.5;
    float barrier_width = 0.2;
    float barrier_height = 2.0 + abs(sample) * 3.0;
    
    float distance_to_barrier = abs(norm_x - barrier_center);
    float tunnel_prob = exp(-barrier_height * max(0.0, barrier_width - distance_to_barrier));
    
    return sample * tunnel_prob * waveform_scale;
}