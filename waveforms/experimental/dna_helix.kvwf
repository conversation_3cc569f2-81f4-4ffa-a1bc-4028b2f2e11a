KVWF   	   dna_helix   experimental   DNA double helix pattern   high   KarmaViz Advanced Generator   1.0;  float compute_waveform_at_x(float x_coord) {
    if (!waveform_enabled || waveform_length <= 0) {
        return 0.0;
    }
    
    float norm_x = clamp(x_coord, 0.0, 1.0);
    float sample = texture(waveform_data, vec2(norm_x, 0.5)).r;
    
    // DNA helix strands
    float helix1 = sin(norm_x * 12.56636 + time * 2.0) * abs(sample);
    float helix2 = sin(norm_x * 12.56636 + 3.14159 + time * 2.0) * abs(sample);
    
    // Base pairs
    float base_pairs = step(0.8, sin(norm_x * 25.13274)) * 0.3;
    
    return (helix1 + helix2 + base_pairs) * waveform_scale;
}