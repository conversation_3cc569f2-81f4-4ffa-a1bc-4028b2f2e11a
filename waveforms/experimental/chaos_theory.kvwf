KVWF      chaos_theory   experimental(   Strange attractors and butterfly effects   low   KarmaViz Advanced Collection   1.0N  // Chaos Theory
float compute_waveform_intensity_at_xy(float x_coord, float y_coord) {
    if (!waveform_enabled || waveform_length <= 0) return 0.0;
    vec2 pos = vec2(x_coord, y_coord);
    float total = 0.0;
    float fft = texture(fft_data, vec2(0.5, 0.5)).r;
    // Strange attractor
    vec2 attractor = pos;
    for (int i = 0; i < 5; i++) {
        float a = 1.4 + fft * 0.6;
        float b = 0.3 + sin(time + float(i)) * 0.1;
        vec2 new_pos;
        new_pos.x = 1.0 - a * attractor.x * attractor.x + attractor.y;
        new_pos.y = b * attractor.x;
        attractor = new_pos;
        float attractor_dist = distance(pos, attractor * waveform_scale * 0.1 + vec2(0.5));
        if (attractor_dist < 0.03) {
            total += (1.0 - attractor_dist / 0.03) * 0.3;
        }
    }
    // Butterfly effect
    float butterfly = sin(pos.x * 20.0 + time * 3.0 + fft * 10.0) * 
                     cos(pos.y * 15.0 + time * 2.0 + fft * 8.0);
    total += (butterfly * 0.5 + 0.5) * 0.4 * fft;
    return clamp(total, 0.0, 1.0);
}
float compute_waveform_at_x(float x_coord) { return 0.0; }