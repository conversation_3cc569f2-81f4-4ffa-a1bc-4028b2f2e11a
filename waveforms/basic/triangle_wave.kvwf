KVWF   
   triangle_wave   basic4   Triangle wave transformation with smooth transitions   low   KarmaViz Advanced Generator   1.0  float compute_waveform_at_x(float x_coord) {
    if (!waveform_enabled || waveform_length <= 0) {
        return 0.0;
    }
    
    float norm_x = clamp(x_coord, 0.0, 1.0);
    float sample = texture(waveform_data, vec2(norm_x, 0.5)).r;
    
    // Triangle wave transformation
    float triangle = 2.0 * abs(fract(sample * 2.0) - 0.5) - 0.5;
    return triangle * waveform_scale;
}