KVWF   	   sine_wave   basic.   Classic sine wave transformation of audio data   low   KarmaViz Generator   1.0S  float compute_waveform_at_x(float x_coord) {
    if (!waveform_enabled || waveform_length <= 0) {
        return 0.0;
    }
    
    float norm_x = clamp(x_coord, 0.0, 1.0);
    float sample = texture(waveform_data, vec2(norm_x, 0.5)).r;
    
    // Apply sine wave transformation
    return sin(sample * 3.14159 * 2.0) * waveform_scale;
}