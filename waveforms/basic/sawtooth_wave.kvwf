KVWF   
   sawtooth_wave   basic%   Sawtooth wave with sharp rising edges   low   KarmaViz Advanced Generator   1.0o  float compute_waveform_at_x(float x_coord) {
    if (!waveform_enabled || waveform_length <= 0) {
        return 0.0;
    }
    
    float norm_x = clamp(x_coord, 0.0, 1.0);
    float sample = texture(waveform_data, vec2(norm_x, 0.5)).r;
    
    // Sawtooth transformation
    float sawtooth = 2.0 * fract(sample * 2.0) - 1.0;
    return sawtooth * waveform_scale;
}