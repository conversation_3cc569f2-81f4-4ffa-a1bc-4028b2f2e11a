KVWF      ocean_waves   organic   Natural ocean wave patterns   high   KarmaViz Advanced Generator   1.0?  float compute_waveform_at_x(float x_coord) {
    if (!waveform_enabled || waveform_length <= 0) {
        return 0.0;
    }
    
    float norm_x = clamp(x_coord, 0.0, 1.0);
    float sample = texture(waveform_data, vec2(norm_x, 0.5)).r;
    
    // Ocean wave simulation
    float wave1 = sin(norm_x * 6.28318 * 2.0 + time * 1.5) * 0.4;
    float wave2 = sin(norm_x * 6.28318 * 3.7 + time * 0.8) * 0.3;
    float wave3 = sin(norm_x * 6.28318 * 7.1 + time * 2.2) * 0.2;
    
    float ocean = wave1 + wave2 + wave3;
    return sample * (1.0 + ocean * 0.5) * waveform_scale;
}