KVWF   	   heartbeat   organic   Heartbeat-like pulsing waveform   high   KarmaViz Generator   1.0+  float compute_waveform_at_x(float x_coord) {
    if (!waveform_enabled || waveform_length <= 0) {
        return 0.0;
    }
    
    float norm_x = clamp(x_coord, 0.0, 1.0);
    float sample = texture(waveform_data, vec2(norm_x, 0.5)).r;
    
    // Create heartbeat pulse
    float beat_time = mod(time * 1.2, 2.0);
    float pulse = exp(-beat_time * 3.0) * sin(beat_time * 20.0);
    pulse += exp(-(beat_time - 0.3) * 8.0) * sin((beat_time - 0.3) * 30.0) * 0.6;
    pulse = max(0.0, pulse) * 0.5 + 0.5;
    
    return sample * pulse * waveform_scale;
}