KVWF      tree_growth   organic   Organic tree growth pattern   high   KarmaViz Advanced Generator   1.0<  float compute_waveform_at_x(float x_coord) {
    if (!waveform_enabled || waveform_length <= 0) {
        return 0.0;
    }
    
    float norm_x = clamp(x_coord, 0.0, 1.0);
    float sample = texture(waveform_data, vec2(norm_x, 0.5)).r;
    
    // Tree growth branching
    float trunk = exp(-abs(norm_x - 0.5) * 8.0);
    float branch1 = exp(-abs(norm_x - 0.3) * 12.0) * step(0.2, abs(sample));
    float branch2 = exp(-abs(norm_x - 0.7) * 12.0) * step(0.2, abs(sample));
    
    float growth = trunk + branch1 + branch2;
    return sample * growth * waveform_scale;
}