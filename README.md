# 🌈 KarmaViz - Advanced Audio Visualizer

<div align="center">

![<PERSON>rmaViz Logo](https://img.shields.io/badge/KarmaViz-Audio%20Visualizer-purple?style=for-the-badge&logo=music&logoColor=white)

[![Python](https://img.shields.io/badge/Python-3.8+-blue?style=flat-square&logo=python)](https://python.org)
[![OpenGL](https://img.shields.io/badge/OpenGL-ModernGL-green?style=flat-square&logo=opengl)](https://moderngl.readthedocs.io/)
[![License](https://img.shields.io/badge/License-Personal%20Use-orange?style=flat-square)](LICENSE.md)

**A cutting-edge, GPU-accelerated audio visualizer for Linux with real-time GLSL shader compilation, advanced waveform rendering, and immersive visual effects.**

[🎵 Features](#-features) • [🚀 Installation](#-installation) • [⌨️ Controls](#️-keyboard-shortcuts) • [☕ Support](#-support-the-project)

</div>

---

## 🎵 Features

### 🎨 **Advanced Visual Effects**
- **GPU-Accelerated Rendering**: Leverages ModernGL for high-performance OpenGL rendering
- **Real-time GLSL Shader Compilation**: Dynamic shader loading with live error reporting
- **Customizable Waveform Rendering**: 100+ built-in waveforms across 12 categories
- **Dynamic Warp Maps**: 3D transformations, distortions, and geometric effects
- **Multi-layered Effects**: Glow, trails, smoke, pulse, bounce, and kaleidoscope effects
- **Advanced Color Palettes**: 30+ carefully crafted color schemes with smooth transitions
- **Spectrogram Overlay**: Spectral analysis for an added visual element**

### 🎵 **Audio Processing**
- **Real-time Audio Analysis**: Advanced FFT processing with beat detection
- **Multiple Audio Sources**: System audio capture with device selection
- **Adaptive Sensitivity**: Dynamic beat detection with configurable thresholds
- **Audio-reactive Parameters**: All visual effects respond to audio characteristics

### 🛠️ **Professional Tools**
- **Live Shader Editor**: Built-in GLSL editor with syntax highlighting and error reporting
- **Waveform Editor**: Create and modify custom waveforms with live preview
- **Warp Map Editor**: Design complex 3D transformations and distortion effects with live preview
- **Preset System**: Save and load complete visualization configurations
- **Performance Monitoring**: Real-time FPS and performance statistics

### 🎮 **User Experience**
- **Intuitive Controls**: Comprehensive keyboard shortcuts for most features
- **GUI Configuration**: Modern Qt-based settings interface
- **Fullscreen Support**: Multiple monitor support with resolution selection
- **Anti-aliasing**: FXAA post-processing for smooth visuals
- **Mouse Interaction**: Optional mouse-reactive effects

---

## 🚀 Installation

### Prerequisites
- **Python 3.8+** (3.9+ recommended)
- **OpenGL 3.3+** compatible graphics card
- **Audio system** (ALSA/PulseAudio/JACK on Linux)

### Quick Install

```bash
# Clone the repository
git clone https://github.com/yourusername/karmaviz.git
cd karmaviz

# Install Python dependencies
pip install -r requirements.txt

# Build Cython extensions (optional, for better performance)
python setup.py build_ext --inplace

# Run KarmaViz
python main.py
```

### Dependencies

**Core Requirements:**
- `pygame` - Window management and input handling
- `moderngl` - OpenGL rendering
- `numpy` - Numerical computations
- `PyQt5` - GUI interface
- `sounddevice` - Audio capture
- `scipy` - Audio processing

**Optional (for better performance):**
- `Cython` - Compiled color operations

### System-Specific Setup

**Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install python3-dev python3-pip portaudio19-dev
sudo apt install libgl1-mesa-dev libglu1-mesa-dev
pip install -r requirements.txt
```

**Arch Linux:**
```bash
sudo pacman -S python python-pip portaudio mesa
pip install -r requirements.txt
```
---

## ⌨️ Keyboard Shortcuts

### 🎮 **Main Controls**
| Key | Action |
|-----|--------|
| `Q` | Quit application |
| `TAB` | Toggle configuration menu |
| `F11` | Toggle fullscreen |
| `P` | Toggle pulse effect |
| `Numpad 5` | Toggle bounce effect |
| `I` | Toggle mouse interaction |
| `S` | Toggle spectrogram overlay |
| `W` | Cycle GPU waveforms |
| `R` | Cycle rotation modes |
| `M` | Cycle symmetry modes |
| `K` | Toggle kaleidoscope effect |
| `L` | Toggle warp-first rendering |

### 🎨 **Visual Effects**
| Key | Action |
|-----|--------|
| `T` / `Shift+T` | Increase/Decrease trail intensity |
| `G` / `Shift+G` | Increase/Decrease glow intensity |
| `F` / `Shift+F` | Increase/Decrease smoke intensity |
| `[` / `]` | Decrease/Increase pulse intensity |
| `↑` / `↓` | Increase/Decrease waveform scale |
| `Shift+↑` / `Shift+↓` | Increase/Decrease glow radius |

### ⚡ **Speed & Animation**
| Key | Action |
|-----|--------|
| `Numpad +` / `Numpad -` | Increase/Decrease animation speed |
| `Numpad *` / `Numpad /` | Increase/Decrease audio speed boost |
| `Numpad 3` / `Numpad 1` | Increase/Decrease palette speed |
| `Numpad 6` / `Numpad 4` | Increase/Decrease color cycle speed |

### 🎵 **Audio & Beat Controls**
| Key | Action |
|-----|--------|
| `.` / `,` | Increase/Decrease beats per change |
| `Numpad 9` / `Numpad 7` | Increase/Decrease beat sensitivity |
| `Numpad 8` / `Numpad 2` | Increase/Decrease bounce intensity |
| `/` | Toggle automatic transitions |

### 🗺️ **Warp Map Controls**
| Key | Action |
|-----|--------|
| `Space` | Manual warp map change |
| `Backspace` | Clear current warp map |

### 🎯 **Preset System**
| Key | Action |
|-----|--------|
| `0-9` | Load quick preset slot (0-9) |
| `Ctrl+0-9` | Save current settings to quick preset slot |

### 📊 **Performance & Debug**
| Key | Action |
|-----|--------|
| `F1` | Print performance statistics |
| `F2` | Clear performance statistics |
| `F3` | Toggle performance monitoring |
| `F4` | Show shader compilation status |

---

## 🎨 Waveform Categories

KarmaViz includes **100+ professionally crafted waveforms** organized into categories:

- **🌊 Basic** - Classic waveform patterns
- **🔬 Advanced** - Complex mathematical visualizations  
- **🌌 Cosmic** - Space-inspired ethereal effects
- **💻 Digital** - Cyberpunk and tech aesthetics
- **🧪 Experimental** - Cutting-edge visual experiments
- **🌀 Fractal** - Self-similar recursive patterns
- **🚀 Futuristic** - Sci-fi inspired designs
- **💡 Lighting** - Dynamic illumination effects
- **📐 Mathematical** - Geometric and algebraic forms
- **🏃 Motion** - Kinetic and flow-based patterns
- **🌿 Natural** - Organic and nature-inspired forms
- **🌱 Organic** - Fluid, life-like movements
- **📼 Retro** - Vintage and nostalgic styles

---

## 🗺️ Warp Map Effects

Transform your visualizations with **50+ warp maps**:

- **🎯 Basic** - Fundamental transformations
- **🌌 Cosmic** - Galactic distortions and stellar effects
- **💻 Digital** - Matrix-style and digital glitch effects
- **🌊 Distortion** - Wave and ripple transformations
- **🧪 Experimental** - Avant-garde visual experiments
- **🌀 Fractal** - Recursive geometric patterns
- **🚀 Futuristic** - Advanced sci-fi transformations
- **📐 Geometric** - Mathematical shape manipulations
- **🔢 Mathematical** - Algorithm-based distortions
- **🏃 Motion** - Dynamic movement effects
- **🌿 Organic** - Natural flow transformations
- **📼 Retro** - Classic visual effects
- **🎭 3D Transformations** - Dimensional manipulations

---

## 🎨 Color Palettes

Choose from **30+ stunning color palettes** or create your own using our intuitive editor:

- **🌈 Rainbow** - Full spectrum gradients
- **🌊 Ocean Themes** - Deep blues and aqua tones
- **🔥 Fire & Energy** - Warm reds, oranges, and yellows
- **🌸 Pastel Dreams** - Soft, dreamy color combinations
- **🌙 Night Sky** - Dark blues with stellar accents
- **🍃 Nature** - Earth tones and forest greens
- **💎 Precious Metals** - Gold, silver, and copper
- **🌺 Floral** - Vibrant flower-inspired palettes
- **🎭 Neon** - Electric and cyberpunk colors
- **🏔️ Arctic** - Cool blues and icy whites

---

## 🛠️ Advanced Features

### 📝 **Live Shader Editor**
- Real-time GLSL compilation
- Syntax highlighting with error detection
- Live preview with automatic updates
- Template system for quick starts
- Error reporting with line numbers

### 🎵 **Waveform Editor**
- Visual waveform design interface
- Mathematical function support
- Live audio-reactive preview
- Category organization system
- Export/import functionality

### 🗺️ **Warp Map Editor**
- 3D transformation designer
- Real-time distortion preview
- Mathematical expression support
- Complex effect layering
- Performance optimization tools

### 💾 **Preset Management**
- Complete state saving/loading
- Quick-access slot system (0-9)
- Automatic shader compilation
- Configuration export/import
- Backup and restore functionality

---

## 🔧 Configuration

### 🖥️ **Display Settings**
- **Resolution**: Native, 1920x1080, 2560x1440, 3840x2160
- **FPS**: 20-120
- **Anti-aliasing**: FXAA post-processing
- **Multi-monitor**: Primary/secondary display selection

### 🎵 **Audio Settings**
- **Input Device**: Uses system default output, or select alternate input in audio settings
- **Sample Rate**: 44.1kHz, 48kHz, 96kHz
- **Buffer Size**: Configurable for latency optimization
- **Beat Detection**: Sensitivity and threshold adjustment

### 🎨 **Visual Settings**
- **Effect Intensities**: Individual control for all effects
- **Color Management**: Palette speed and transition settings
- **Animation Speed**: Global and per-preset timing
- **Quality Settings**: Performance vs. visual quality balance

---

## 🚀 Performance

KarmaViz is optimized for high performance:

- **GPU Acceleration**: All rendering on graphics card
- **Threaded Compilation**: Background shader processing
- **Cython Extensions**: Optimized color operations
- **Memory Management**: Efficient buffer handling

**Recommended Specs:**
- **CPU**: Intel i5-8400 / AMD Ryzen 5 2600 or better
- **GPU**: GTX 1060 / RX 580 or better (OpenGL 3.3+) with 2GB+ Graphics Memory
- **Graphics Drivers**: Latest stable releases
- **RAM**: 8GB+ (16GB recommended)
- **Storage**: SSD recommended for shader loading

---

## 🐛 Troubleshooting

### Common Issues

**Audio not working:**
```bash
# Linux: Check audio devices
python -c "import sounddevice; print(sounddevice.query_devices())"

# Install additional audio libraries if needed
sudo apt install pulseaudio-dev portaudio19-dev
```

**OpenGL errors:**
```bash
# Update graphics drivers
# Linux: Install mesa-utils
sudo apt install mesa-utils
glxinfo | grep "OpenGL version"
```

**Performance issues:**
- Lower FPS limit in settings
- Disable anti-aliasing
- Reduce effect intensities
- Close other GPU-intensive applications

**Shader compilation errors:**
- Check GPU OpenGL version (3.3+ required)
- Update graphics drivers
- Try different waveforms/warp maps

---

## 🤝 Contributing

KarmaViz welcomes contributions! Here's how you can help:

### 🎨 **Create Content**
- Design new waveforms (GLSL)
- Create warp map effects
- Develop color palettes
- Share preset configurations

### 🐛 **Report Issues**
- Bug reports with system information
- Performance optimization suggestions
- Feature requests and ideas
- Documentation improvements

### 💻 **Code Contributions**
- Performance optimizations
- New visual effects
- Audio processing improvements
- Cross-platform compatibility

**Development Setup:**
```bash
git clone https://github.com/yourusername/karmaviz.git
cd karmaviz
pip install -e .
python -m pytest tests/  # Run tests
```

---

## 📄 License

KarmaViz is licensed for **Personal Use Only**. 

### ✅ **Permitted Uses**
- Personal entertainment and visualization
- Educational purposes and learning
- Personal creative projects
- Private demonstrations

### ❌ **Prohibited Uses**
- Commercial performances or events
- Public performances or exhibitions
- Distribution of modified versions
- Any revenue-generating activities

For commercial licensing, please contact: **<EMAIL>**

See [LICENSE.md](LICENSE.md) for complete terms.

---

## ☕ Support the Project

**KarmaViz represents hundreds of hours of passionate development work!** 

This project features:
- 🎨 **100+ hand-crafted waveforms** with mathematical precision
- 🗺️ **50+ custom warp maps** for stunning 3D effects  
- 🎵 **Advanced audio processing** with real-time beat detection
- 🛠️ **Professional editing tools** with live preview capabilities
- ⚡ **GPU-optimized rendering** for smooth 60+ FPS performance
- 🎮 **Intuitive controls** with comprehensive keyboard shortcuts
- 🎨 **30+ color palettes** designed by a visual artist
- 📝 **Live shader compilation** with error reporting
- 💾 **Complete preset system** for saving your creations

### 🙏 **Show Your Appreciation**

If KarmaViz has enhanced your music experience or inspired your creativity, consider supporting its continued development:

<div align="center">

[![Buy Me A Coffee](https://img.shields.io/badge/Buy%20Me%20A%20Coffee-Support%20KarmaViz-orange?style=for-the-badge&logo=buy-me-a-coffee&logoColor=white)](https://buymeacoffee.com/karmaviz)

**[☕ Buy me a coffee](https://buymeacoffee.com/karmaviz)**

</div>

Your support helps:
- 🚀 **Accelerate development** of new features
- 🎨 **Create more visual content** (waveforms, effects, palettes)
- 🐛 **Maintain and improve** existing functionality  
- 📚 **Expand documentation** and tutorials
- 🌍 **Support the open-source community**

### 💝 **Other Ways to Support**
- ⭐ **Star this repository** to show your appreciation
- 🐛 **Report bugs** and suggest improvements
- 🎨 **Share your creations** and presets with the community
- 📢 **Spread the word** about KarmaViz to fellow music lovers
- 💻 **Contribute code** or documentation improvements

---

## 🌟 Acknowledgments

**Special thanks to:**
- The **ModernGL** community for excellent OpenGL bindings
- **PyQt5** developers for the robust GUI framework
- **NumPy/SciPy** teams for powerful numerical computing
- **Pygame** community for multimedia support
- All **beta testers** and **contributors** who helped shape KarmaViz

---

## 📞 Contact

- **🐛 Issues & Bugs**: [GitHub Issues](https://github.com/KarmaSwint/KarmaViz/issues)
- **💼 Commercial Licensing**: <EMAIL>
- **☕ Support Development**: [Buy Me A Coffee](https://buymeacoffee.com/karmaviz)
- **💬 Feedback** and Suggestions: <EMAIL>
---

<div align="center">

**Made with ❤️ and countless hours of dedication**

*Transform your music into mesmerizing visual art with KarmaViz*

[![Buy Me A Coffee](https://img.shields.io/badge/☕-Support%20This%20Project-orange?style=flat-square)](https://buymeacoffee.com/karmaviz)

</div>