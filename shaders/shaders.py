VERTEX_SHADER = """//glsl
#version 330
in vec2 in_position;
in vec2 in_texcoord;
out vec2 uv;
void main() {
    gl_Position = vec4(in_position, 0.0, 1.0);
    uv = in_texcoord;
}"""

FRAGMENT_SHADER = """//glsl
#version 330
uniform sampler2D texture0;
uniform sampler2D warp_coords;  // Warp coordinate texture (optional)
uniform bool use_warp_coords;  // Whether to use warp coordinates
uniform float time;
uniform float animation_speed;  // Unified animation speed control

// Waveform rendering uniforms
uniform sampler2D waveform_data;  // 2D texture containing waveform samples (height=1)
uniform sampler2D fft_data;      // 2D texture containing FFT frequency data (height=1)
uniform int waveform_length;     // Number of samples in waveform
uniform float waveform_scale;    // Scale factor for waveform amplitude
uniform bool waveform_enabled;   // Whether to render waveform
uniform vec3 waveform_color;     // Current waveform color from palette
uniform float waveform_alpha;    // Alpha for waveform fade-in effect
uniform float rotation;
uniform float trail_intensity;
uniform float glow_intensity;
uniform float glow_radius;
uniform int symmetry_mode;
uniform int kaleidoscope_sections;  // Number of sections for kaleidoscope mode
uniform float smoke_intensity;  // Controls smoke/diffusion effect
uniform float pulse_scale;  // Added uniform for pulse scale
uniform vec2 mouse_position;  // Add mouse position uniform
uniform vec2 resolution;  // Add resolution uniform
uniform bool mouse_enabled;  // Add mouse interaction toggle
uniform bool bounce_enabled; // Toggle for bounce effect
uniform float bounce_height; // Height of bounce effect
in vec2 uv;
out vec4 fragColor;

vec2 apply_zoom(vec2 pos, float zoom_amount) {
    vec2 center = vec2(0.5, 0.5);

    return center + (pos - center) * (1.0 / zoom_amount); // Fixed spacing around division operator
}

float get_pulse(float t, float amp) {
    float base_pulse = sin(t * 2.0) * 0.5 + 0.5;
    float amp_pulse = pow(amp, 2.0) * 0.5;
    return 1.0 + (base_pulse * 0.1 + amp_pulse * 0.3) * 0.2; // Fixed trailing zero
}

// Symmetry functions moved to separate SYMMETRY_FRAGMENT_SHADER








// WAVEFORM_RENDER_PLACEHOLDER

// Function to render waveform glow effect
float waveform_glow(vec2 pos, float waveform_y, float line_width, float glow_width) {
    float distance_to_line = abs(pos.y - waveform_y);

    // Core line
    float core = 1.0 - smoothstep(0.0, line_width, distance_to_line);

    // Glow effect
    float glow = 1.0 - smoothstep(line_width, glow_width, distance_to_line);
    glow = pow(glow, 1.5) * 0.3;  // Softer glow falloff

    return core + glow * 2;
}

void main() {
    // Initialize position from UV coordinates or warp coordinates
    vec2 pos;
    if (use_warp_coords) {
        // Sample warped coordinates from warp coordinate texture
        vec4 warp_data = texture(warp_coords, uv);
        pos = warp_data.xy;  // Use R and G channels as X and Y coordinates
    } else {
        // Use original UV coordinates
        pos = uv;
    }

    // Handle pulse scale effect
    // This scales the texture coordinates to create a zoom effect while keeping vertices fixed
    if (pulse_scale != 1.0) {
        // Calculate reciprocal of pulse_scale for UV adjustment
        // pulse_scale > 1 zooms in by making UV range smaller
        // pulse_scale < 1 zooms out by making UV range larger
        float uv_scale = 1.0 / pulse_scale;

        // Calculate offset to keep zoom centered in screen
        // As UV range changes, we need to shift it to maintain center point
        float uv_offset = (1.0 - uv_scale) * 0.5;

        // Apply the scale and offset to position
        pos = pos * uv_scale + uv_offset;
    }

    // Calculate animation time using unified speed control
    float t = time * animation_speed;

    // Apply rotation FIRST so warp maps and waveform operate on rotated coordinates
    // Convert rotation degrees to radians
    float rotation_angle = radians(rotation * 3);
    vec2 center = vec2(0.5, 0.5);

    // Translate to origin, rotate, translate back
    pos -= center;
    pos = vec2(
        pos.x * cos(rotation_angle) - pos.y * sin(rotation_angle),
        pos.x * sin(rotation_angle) + pos.y * cos(rotation_angle)
    );
    pos += center;

    // Store original position for mouse calculations (after rotation)
    vec2 orig_pos = pos;

    // Calculate mouse values but don't apply them yet
    vec2 mouse_pos = mouse_position / resolution;
    float mouse_dist = distance(orig_pos, mouse_pos);
    float mouse_influence = 0.05 / (mouse_dist + 0.05);

    // Apply mouse interaction only if enabled
    if (mouse_enabled) {
        pos += (orig_pos - mouse_pos) * mouse_influence;
    }

    // No symmetry in main shader - will be applied in separate symmetry pass
    vec2 final_sample_pos = pos;

    // Initialize output color
    vec4 color = vec4(0.0);



    // Apply bounce effect if enabled
    if (bounce_enabled) {
        // Adjust the sample position based on bounce height
        final_sample_pos.y += bounce_height;
    }

    // Calculate trail effect alpha
    // Higher trail_intensity creates longer-lasting trails
    // trail_intensity: 0.0 (short trail, alpha=0.90) to 5.0 (very long trail, alpha~0.999)
    float trail_alpha = 0.90 + (0.02 * trail_intensity);
    trail_alpha = min(trail_alpha, 0.999);

    // Apply smoke diffusion effect if enabled
    if (smoke_intensity > 0.0) {
        // Scale of random noise effect
        float noise_scale = smoke_intensity * 0.02;
        float time_factor = t;

        // Sample 4 times with decreasing offset for smooth trails
        for(int i = 0; i < 4; i++) {
            float blend = float(i) / 4.0;

            // Create smooth random offset using sine waves
            vec2 noise_offset = vec2(
                sin(final_sample_pos.x * 10.0 + time_factor + blend * 6.28) * noise_scale,
                cos(final_sample_pos.y * 10.0 + time_factor + blend * 6.28) * noise_scale
            );

            // Sample texture with combined offsets
            vec2 samplePos = fract(final_sample_pos + noise_offset);
            color += texture(texture0, samplePos) * (0.25 * glow_intensity);
        }
    } else {
        // No smoke effect - just sample with pattern offset
        for(int i = 0; i < 4; i++) {
            float blend = float(i) / 4.0;
            vec2 samplePos = fract(final_sample_pos);
            color += texture(texture0, samplePos) * (0.25 * glow_intensity);
        }
    }

    // Add mouse glow effect only if enabled
    if (mouse_enabled) {
        float mouse_glow = mouse_influence * 0.005;
        color.rgb += vec3(mouse_glow);
    }

    // Add GPU waveform rendering AFTER symmetry transformations
    if (waveform_enabled) {
        float waveform_intensity = 0.0;
        
        // Try new fragment-based waveform function first (if it exists)
        // This allows waveforms to draw bars, particles, text, etc.
        #ifdef FRAGMENT_WAVEFORM
            waveform_intensity = compute_waveform_intensity_at_xy(final_sample_pos.x, final_sample_pos.y);
        #else
            // Fallback to traditional line-based waveform for backward compatibility
            float waveform_sample = compute_waveform_at_x(final_sample_pos.x);

            // Single centered waveform with bidirectional peaks
            float center_y = 0.5;
            float base_amplitude = 0.25;  // Base amplitude (quarter screen height)

            // Render as single centered waveform with bidirectional peaks
            float centered_waveform_y = center_y + waveform_sample * base_amplitude;
            float line_width = 0.04;   // Core line thickness
            waveform_intensity = waveform_glow(final_sample_pos, centered_waveform_y, line_width, glow_radius);
        #endif

        // Scale the waveform contribution for good visibility without blowout
        vec4 waveform_contribution = vec4(waveform_color * waveform_intensity * 0.4 * waveform_alpha, 0.0);

        // Blend waveform with the existing color
        color.rgb += waveform_contribution.rgb;
    }

    // Clamp to prevent white blowout
    color.rgb = clamp(color.rgb, 0.0, 0.97);

    // Output final color with trail alpha
    fragColor = vec4(color.rgb, trail_alpha);
}
"""
# Spectrogram overlay shaders
SPECTROGRAM_VERTEX_SHADER = """//glsl
    #version 330

    in vec2 in_position;
    in vec2 in_texcoord;
    out vec2 uv;

    void main() {
        gl_Position = vec4(in_position, 0.0, 1.0);
        uv = in_texcoord;
    }
"""

SPECTROGRAM_FRAGMENT_SHADER = """//glsl
    #version 330

    uniform sampler2D frequency_data;
    uniform float opacity;
    uniform sampler2D palette_data;  // Palette colors as texture
    uniform int palette_size;        // Number of colors in palette
    uniform float time;              // For color cycling
    uniform float color_interpolation_speed; // Speed of color transitions

    in vec2 uv;
    out vec4 fragColor;

    // Function to get palette color with smooth interpolation
    vec3 get_palette_color(float t) {
        if (palette_size <= 0) {
            return vec3(1.0, 1.0, 1.0); // Fallback to white
        }

        // Wrap t to [0, 1] range
        t = fract(t);

        // Scale to palette range
        float scaled_t = t * float(palette_size - 1);
        int index1 = int(floor(scaled_t));
        int index2 = (index1 + 1) % palette_size;
        float blend = fract(scaled_t);

        // Sample colors from palette texture
        vec3 color1 = texture(palette_data, vec2(float(index1) / float(palette_size - 1), 0.5)).rgb;
        vec3 color2 = texture(palette_data, vec2(float(index2) / float(palette_size - 1), 0.5)).rgb;

        // Smooth interpolation between colors
        return mix(color1, color2, smoothstep(0.0, 1.0, blend));
    }

    void main() {
        // Only render in top 30% (y > 0.7) and bottom 30% (y < 0.3) of screen
        if (uv.y > 0.3 && uv.y < 0.7) {
            discard;
        }

        // Determine if we're in top or bottom region
        bool is_top = uv.y > 0.7;
        float region_y;

        if (is_top) {
            // Top region: flip vertically so bars point downward
            // Map y from [0.7, 1.0] to [1.0, 0.0] (flipped)
            region_y = 1.0 - (uv.y - 0.7) / 0.3;
        } else {
            // Bottom region: normal orientation so bars point upward
            // Map y from [0.0, 0.3] to [0.0, 1.0] (normal)
            region_y = uv.y / 0.3;
        }

        float mirrored_x = uv.x < 0.5 ? uv.x * 2.0 : (1.0 - uv.x) * 2.0;
        float freq = texture(frequency_data, vec2(mirrored_x, 0.5)).r;

        // Create height-based intensity falloff within the 30% regions
        float height_intensity = 1.0 - smoothstep(0.0, 1.0, region_y);

        // Combine frequency and height for final intensity
        float final_intensity = freq * height_intensity;

        // Map frequency amplitude (height) to palette color with time-based cycling
        // Use the frequency value (height of the bar) to select palette color
        float color_t = freq + time * color_interpolation_speed;
        vec3 base_color = get_palette_color(color_t);

        // Apply intensity to the palette color
        vec3 final_color = base_color * final_intensity;

        // Add some brightness boost for better visibility
        final_color *= 1.5;

        fragColor = vec4(final_color, final_intensity * opacity);
    }
"""

# Separate symmetry shader for dedicated symmetry processing
SYMMETRY_VERTEX_SHADER = """//glsl
#version 330
in vec2 in_position;
in vec2 in_texcoord;
out vec2 uv;
void main() {
    gl_Position = vec4(in_position, 0.0, 1.0);
    uv = in_texcoord;
}"""

SYMMETRY_FRAGMENT_SHADER = """//glsl
#version 330
uniform sampler2D texture0;  // Input texture from main waveform pass
uniform float time;
uniform float animation_speed;
uniform int symmetry_mode;
uniform int kaleidoscope_sections;
uniform vec2 resolution;
uniform vec2 mouse_position;
uniform bool mouse_enabled;

in vec2 uv;
out vec4 fragColor;

vec2 apply_symmetry(vec2 pos, float t, int mode) {
    vec2 local_pos = pos;
    vec2 centered = pos - 0.5;  // Define centered coordinates for all modes

    if (mode == 1) {  // Mirror
        return vec2(abs(centered.x), centered.y) + 0.5;
    }
    else if (mode == 2) {  // Quad mirror
        return vec2(abs(centered.x), abs(centered.y)) + 0.5;
    }
    else if (mode == 3) {  // Kaleidoscope
        // Create kaleidoscope effect with configurable number of sections
        float angle = atan(centered.y, centered.x);
        float radius = length(centered);

        // Normalize angle to [0, 2π]
        angle = mod(angle + 2.0 * 3.14159, 2.0 * 3.14159);

        // Create sections based on kaleidoscope_sections uniform
        float section_angle = 2.0 * 3.14159 / float(kaleidoscope_sections);
        float section = floor(angle / section_angle);
        float normalized_angle = mod(angle, section_angle);

        // Mirror every other section for kaleidoscope effect
        if (mod(section, 2.0) >= 1.0) {
            normalized_angle = section_angle - normalized_angle;
        }

        return vec2(
            radius * cos(normalized_angle),
            radius * sin(normalized_angle)
        ) + 0.5;
    }
    else if (mode == 4) {  // Grid symmetry
        // Randomly change grid size based on time
        float base_grid = 5.0;  // Minimum grid size
        float grid_range = 5.0;  // Maximum additional cells
        float grid_size = base_grid + mod(t * 0.5, grid_range);  // Time-based grid size between 5-20 (use t)

        vec2 cell = floor(pos * grid_size) / grid_size;  // Cell coordinates
        vec2 cell_pos = fract(pos * grid_size);  // Position within cell

        // Mirror within each cell
        cell_pos = vec2(
            cell_pos.x > 0.5 ? 1.0 - cell_pos.x : cell_pos.x,
            cell_pos.y > 0.5 ? 1.0 - cell_pos.y : cell_pos.y
        );

        return cell + cell_pos / grid_size;
    }
    else if (mode == 5) {  // Fractal Mirror
        // Create fractal-like mirroring with different effects per section
        float angle = atan(centered.y, centered.x);
        float radius = length(centered);

        // Normalize angle to [0, 2π]
        angle = mod(angle + 2.0 * 3.14159, 2.0 * 3.14159);

        // Create 6 sections for variety
        float section_angle = 2.0 * 3.14159 / 6.0;
        float section_id = floor(angle / section_angle);

        // Apply different warping effects to alternating sections
        if (mod(section_id, 3.0) == 0.0) {
            // Convex mirror effect (fish-eye)
            float radius = length(centered);
            radius = sqrt(radius) * 0.8; // Compress radially
            local_pos = 0.5 + normalize(centered) * radius;
        } else if (mod(section_id, 3.0) == 1.0) {
            // Concave mirror effect (zoom in)
            float radius = length(centered);
            radius = radius * radius * 1.5; // Expand radially
            local_pos = 0.5 + normalize(centered) * min(radius, 0.5);
        } else {
            // Wavy mirror effect
            float wave_freq = 8.0;
            float wave_amp = 0.1;
            vec2 wave_offset = vec2(
                sin(centered.y * wave_freq + t * 2.0) * wave_amp,
                cos(centered.x * wave_freq + t * 2.0) * wave_amp
            );
            local_pos = pos + wave_offset;
        }

        // Ensure coordinates stay within bounds
        return fract(local_pos);
    }
    else if (mode == 6) {  // Hexagonal Symmetry
        // Create 6-fold rotational symmetry like a snowflake
        float angle = atan(centered.y, centered.x);
        float radius = length(centered);

        // Normalize angle to [0, 2π]
        angle = mod(angle + 2.0 * 3.14159, 2.0 * 3.14159);

        // Create 6 sections (60 degrees each)
        float section_angle = 2.0 * 3.14159 / 6.0;
        float section = floor(angle / section_angle);
        float normalized_angle = mod(angle, section_angle);

        // Mirror every other section for hexagonal symmetry
        if (mod(section, 2.0) >= 1.0) {
            normalized_angle = section_angle - normalized_angle;
        }

        return vec2(
            radius * cos(normalized_angle),
            radius * sin(normalized_angle)
        ) + 0.5;
    }
    else if (mode == 7) {  // Spiral Symmetry
        // Create spiral-based symmetry with time-based rotation
        float angle = atan(centered.y, centered.x);
        float radius = length(centered);

        // Add spiral effect
        float spiral_factor = 3.0;  // Controls tightness of spiral
        float spiral_angle = angle + radius * spiral_factor + t * 0.5;

        // Normalize to create repeating pattern
        spiral_angle = mod(spiral_angle, 2.0 * 3.14159 / 4.0) * 4.0;

        return vec2(
            radius * cos(spiral_angle),
            radius * sin(spiral_angle)
        ) + 0.5;
    }
    else if (mode == 8) {  // Mandala Symmetry
        // Create complex mandala-like symmetry with multiple reflection axes
        float angle = atan(centered.y, centered.x);
        float radius = length(centered);

        // Normalize angle to [0, 2π]
        angle = mod(angle + 2.0 * 3.14159, 2.0 * 3.14159);

        // Create 12 sections (30 degrees each) for detailed mandala
        float section_angle = 2.0 * 3.14159 / 12.0;
        float section = floor(angle / section_angle);
        float normalized_angle = mod(angle, section_angle);

        // Create complex mirroring pattern
        // Mirror every other section
        if (mod(section, 2.0) >= 1.0) {
            normalized_angle = section_angle - normalized_angle;
        }

        // Add radial mirroring for mandala effect
        float radial_sections = 3.0;
        float radial_section = floor(radius * radial_sections);
        if (mod(radial_section, 2.0) >= 1.0) {
            radius = fract(radius * radial_sections) / radial_sections;
        } else {
            radius = (1.0 - fract(radius * radial_sections)) / radial_sections + radial_section / radial_sections;
        }

        return vec2(
            radius * cos(normalized_angle),
            radius * sin(normalized_angle)
        ) + 0.5;
    }
    else if (mode == 9) {  // Flower Symmetry
        // Create flower-like symmetry with petal effects
        float angle = atan(centered.y, centered.x);
        float radius = length(centered);

        // Normalize angle to [0, 2π]
        angle = mod(angle + 2.0 * 3.14159, 2.0 * 3.14159);

        // Create 8 petals
        float petal_angle = 2.0 * 3.14159 / 8.0;
        float petal = floor(angle / petal_angle);
        float normalized_angle = mod(angle, petal_angle);

        // Create petal shape by modulating radius
        float petal_shape = sin(normalized_angle * 8.0 / petal_angle) * 0.3 + 0.7;
        radius *= petal_shape;

        // Mirror alternating petals
        if (mod(petal, 2.0) >= 1.0) {
            normalized_angle = petal_angle - normalized_angle;
        }

        return vec2(
            radius * cos(normalized_angle),
            radius * sin(normalized_angle)
        ) + 0.5;
    }
    else if (mode == 10) {  // Triangular Symmetry
        // Create 3-fold rotational symmetry like a triangle or triquetra
        float angle = atan(centered.y, centered.x);
        float radius = length(centered);

        // Normalize angle to [0, 2π]
        angle = mod(angle + 2.0 * 3.14159, 2.0 * 3.14159);

        // Create 3 sections (120 degrees each)
        float section_angle = 2.0 * 3.14159 / 3.0;
        float section = floor(angle / section_angle);
        float normalized_angle = mod(angle, section_angle);

        // Mirror every other section for triangular symmetry
        if (mod(section, 2.0) >= 1.0) {
            normalized_angle = section_angle - normalized_angle;
        }

        return vec2(
            radius * cos(normalized_angle),
            radius * sin(normalized_angle)
        ) + 0.5;
    }

    return pos;  // No symmetry (mode 0)
}

void main() {
    vec2 pos = uv;
    float t = time * animation_speed;

    // Apply mouse interaction if enabled
    if (mouse_enabled) {
        vec2 mouse_pos = mouse_position / resolution;
        float mouse_dist = distance(pos, mouse_pos);
        float mouse_influence = 0.05 / (mouse_dist + 0.05);
        pos += (pos - mouse_pos) * mouse_influence;
    }

    // Apply symmetry transformation
    vec2 symmetry_pos = apply_symmetry(pos, t, symmetry_mode);

    // Sample the input texture with symmetry transformation
    vec4 color = texture(texture0, symmetry_pos);

    // Add mouse glow effect only if enabled
    if (mouse_enabled) {
        vec2 mouse_pos = mouse_position / resolution;
        float mouse_dist = distance(pos, mouse_pos);
        float mouse_influence = 0.05 / (mouse_dist + 0.05);
        float mouse_glow = mouse_influence * 0.005;
        color.rgb += vec3(mouse_glow);
    }

    fragColor = color;
}
"""

# Separate warp map shader for dedicated warp processing
WARP_MAP_VERTEX_SHADER = """//glsl
#version 330
in vec2 in_position;
in vec2 in_texcoord;
out vec2 uv;
void main() {
    gl_Position = vec4(in_position, 0.0, 1.0);
    uv = in_texcoord;
}"""

WARP_MAP_FRAGMENT_SHADER = """//glsl
#version 330
uniform float time;
uniform float animation_speed;  // Unified animation speed control
uniform float warp_intensity;
uniform int active_warp_map;
uniform vec2 resolution;  // Screen resolution for aspect ratio correction
in vec2 uv;
out vec4 fragColor;

// WARP_MAP_FUNCTIONS_PLACEHOLDER

vec2 apply_warp(vec2 pos, float t, int warp_index) {
    // WARP_DISPATCHER_PLACEHOLDER
    return vec2(0.0); // Default: no warp offset
}

void main() {
    // Initialize position from UV coordinates
    vec2 pos = uv;
    
    // Calculate animation time using unified speed control
    float t = time * animation_speed;
    
    // Apply warp map transformation to the coordinate space
    // Normalize coordinates to square aspect ratio for consistent warp behavior
    vec2 aspect_corrected_pos = pos;
    float aspect_ratio = resolution.x / resolution.y;
    if (aspect_ratio > 1.0) {
        // Wide screen - compress X
        aspect_corrected_pos.x = (pos.x - 0.5) / aspect_ratio + 0.5;
    } else {
        // Tall screen - compress Y
        aspect_corrected_pos.y = (pos.y - 0.5) * aspect_ratio + 0.5;
    }
    
    // Get warp offset from the warp map function
    vec2 warp_offset = apply_warp(aspect_corrected_pos, t, active_warp_map);
    
    // Transform warp offset back to screen space
    if (aspect_ratio > 1.0) {
        warp_offset.x *= aspect_ratio;
    } else {
        warp_offset.y /= aspect_ratio;
    }
    
    // Apply warp intensity and calculate final warped coordinates
    vec2 warped_pos = pos + warp_offset * warp_intensity;
    
    // Ensure coordinates stay within bounds
    warped_pos = fract(warped_pos);
    
    // Output the warped coordinates as color data
    // R and G channels contain the warped X and Y coordinates
    // B channel is unused, A channel is 1.0
    fragColor = vec4(warped_pos.x, warped_pos.y, 0.0, 1.0);
}
"""

# Warp application shader - applies warp coordinates to an input texture
WARP_APPLICATION_VERTEX_SHADER = """//glsl
#version 330
in vec2 in_position;
in vec2 in_texcoord;
out vec2 uv;
void main() {
    gl_Position = vec4(in_position, 0.0, 1.0);
    uv = in_texcoord;
}"""

WARP_APPLICATION_FRAGMENT_SHADER = """//glsl
#version 330
uniform sampler2D input_texture;  // Input texture to be warped
uniform sampler2D warp_coords;    // Warp coordinate texture
uniform float warp_intensity;

in vec2 uv;
out vec4 fragColor;

void main() {
    // Sample warp coordinates
    vec4 warp_data = texture(warp_coords, uv);
    vec2 warped_pos = warp_data.xy;

    // Apply warp intensity
    vec2 final_pos = mix(uv, warped_pos, warp_intensity);

    // Sample input texture at warped coordinates
    vec4 color = texture(input_texture, final_pos);

    fragColor = color;
}
"""
