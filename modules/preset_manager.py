"""
Preset Manager for KarmaViz

This module handles saving and loading complete visualizer presets that capture:
- All visualizer settings and parameters
- Current palette information
- Current warp map with shader code
- Audio processing settings
- Effect configurations

Presets are stored as compressed .kviz files using a compact binary format.
"""

import os
import time
import gzip
import pickle
import struct
import json
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from pathlib import Path

# Import shader compilation status for threaded compilation
try:
    from modules.shader_compiler import CompilationStatus
except ImportError:
    # Fallback if import fails
    from enum import Enum
    class CompilationStatus(Enum):
        ''' Status of shader compilation'''
        COMPLETED = "completed"

# KarmaViz preset format constants
KVIZ_MAGIC = b'KVIZ'  # File magic number
KVIZ_VERSION = 1      # Format version
KVIZ_EXTENSION = '.kviz'

# Compression levels
COMPRESSION_NONE = 0
COMPRESSION_GZIP = 1
COMPRESSION_PICKLE = 2

# Data type identifiers for compact encoding
TYPE_FLOAT = 0x01
TYPE_INT = 0x02
TYPE_BOOL = 0x03
TYPE_STRING = 0x04
TYPE_LIST = 0x05
TYPE_DICT = 0x06


@dataclass
class PresetInfo:
    """Complete preset information"""
    name: str
    description: str
    created_date: str
    author: str
    version: str

    # Visualizer state
    visualizer_settings: Dict[str, Any]

    # Palette information
    palette_info: Dict[str, Any]

    # Warp map information
    warp_map_info: Dict[str, Any]

    # Waveform information
    waveform_info: Dict[str, Any]

    # Audio settings
    audio_settings: Dict[str, Any]

    # Effect settings
    effect_settings: Dict[str, Any]

    # Metadata
    tags: List[str] = None
    thumbnail_path: str = ""
    file_path: str = ""


class PresetManager:
    """Manages saving and loading of KarmaViz presets"""

    def __init__(self, presets_dir: str = "presets"):
        self.presets_dir = Path(presets_dir)
        self.presets_dir.mkdir(exist_ok=True)

        # Create subdirectories
        self.quick_presets_dir = self.presets_dir / "quick"
        self.quick_presets_dir.mkdir(exist_ok=True)
        self.user_presets_dir = self.presets_dir / "user"
        self.user_presets_dir.mkdir(exist_ok=True)

        self.presets: Dict[str, PresetInfo] = {}
        self.compression_level = COMPRESSION_GZIP  # Default compression
        self.load_all_presets()

    def _encode_compact_data(self, data: Any) -> bytes:
        """Encode data in a compact binary format"""
        if isinstance(data, float):
            return struct.pack('Bf', TYPE_FLOAT, data)
        elif isinstance(data, int):
            return struct.pack('Bi', TYPE_INT, data)
        elif isinstance(data, bool):
            return struct.pack('B?', TYPE_BOOL, data)
        elif isinstance(data, str):
            encoded_str = data.encode('utf-8')
            return struct.pack('BI', TYPE_STRING, len(encoded_str)) + encoded_str
        elif isinstance(data, list):
            result = struct.pack('BI', TYPE_LIST, len(data))
            for item in data:
                result += self._encode_compact_data(item)
            return result
        elif isinstance(data, dict):
            result = struct.pack('BI', TYPE_DICT, len(data))
            for key, value in data.items():
                result += self._encode_compact_data(key)
                result += self._encode_compact_data(value)
            return result
        else:
            # Fallback to string representation
            return self._encode_compact_data(str(data))

    def _decode_compact_data(self, data: bytes, offset: int = 0) -> tuple[Any, int]:
        """Decode compact binary data, returns (value, new_offset)"""
        if offset >= len(data):
            raise ValueError("Unexpected end of data")

        data_type = data[offset]
        offset += 1

        if data_type == TYPE_FLOAT:
            value = struct.unpack_from('f', data, offset)[0]
            return value, offset + 4
        elif data_type == TYPE_INT:
            value = struct.unpack_from('i', data, offset)[0]
            return value, offset + 4
        elif data_type == TYPE_BOOL:
            value = struct.unpack_from('?', data, offset)[0]
            return value, offset + 1
        elif data_type == TYPE_STRING:
            length = struct.unpack_from('I', data, offset)[0]
            offset += 4
            value = data[offset:offset + length].decode('utf-8')
            return value, offset + length
        elif data_type == TYPE_LIST:
            length = struct.unpack_from('I', data, offset)[0]
            offset += 4
            result = []
            for _ in range(length):
                item, offset = self._decode_compact_data(data, offset)
                result.append(item)
            return result, offset
        elif data_type == TYPE_DICT:
            length = struct.unpack_from('I', data, offset)[0]
            offset += 4
            result = {}
            for _ in range(length):
                key, offset = self._decode_compact_data(data, offset)
                value, offset = self._decode_compact_data(data, offset)
                result[key] = value
            return result, offset
        else:
            raise ValueError(f"Unknown data type: {data_type}")

    def _save_kviz_format(self, preset_info: PresetInfo, filepath: Path) -> bool:
        """Save preset in compact .kviz format"""
        try:
            print(f"🔍 [DEBUG] Saving preset to {filepath}")
            # Convert preset to dictionary
            preset_dict = asdict(preset_info)
            print(f"🔍 [DEBUG] Preset dict keys: {list(preset_dict.keys())}")
            print(f"🔍 [DEBUG] Visualizer settings keys: {list(preset_dict.get('visualizer_settings', {}).keys())}")
            print(f"🔍 [DEBUG] Waveform name in preset: {preset_dict.get('visualizer_settings', {}).get('current_waveform_name', 'NOT_SET')}")

            # Create header
            header = struct.pack('4sII', KVIZ_MAGIC, KVIZ_VERSION, self.compression_level)
            print(f"🔍 [DEBUG] Using compression level: {self.compression_level}")

            # Serialize data based on compression level
            if self.compression_level == COMPRESSION_NONE:
                # Use compact binary encoding
                data = self._encode_compact_data(preset_dict)
            elif self.compression_level == COMPRESSION_GZIP:
                # Use gzip compression on JSON
                json_data = json.dumps(preset_dict, separators=(',', ':')).encode('utf-8')
                print(f"🔍 [DEBUG] JSON data length: {len(json_data)}")
                print(f"🔍 [DEBUG] JSON preview: {json_data[:200]}...")
                data = gzip.compress(json_data, compresslevel=9)
                print(f"🔍 [DEBUG] Compressed data length: {len(data)}")
            elif self.compression_level == COMPRESSION_PICKLE:
                # Use pickle with gzip compression
                pickle_data = pickle.dumps(preset_dict, protocol=pickle.HIGHEST_PROTOCOL)
                data = gzip.compress(pickle_data, compresslevel=9)
            else:
                raise ValueError(f"Unknown compression level: {self.compression_level}")

            # Write file
            with open(filepath, 'wb') as f:
                f.write(header)
                f.write(struct.pack('I', len(data)))  # Data length
                f.write(data)

            print(f"🔍 [DEBUG] File written successfully, total size: {filepath.stat().st_size} bytes")
            return True

        except Exception as e:
            print(f"Error saving .kviz format: {e}")
            return False

    def _load_kviz_format(self, filepath: Path) -> Optional[PresetInfo]:
        """Load preset from .kviz format"""
        try:
            with open(filepath, 'rb') as f:
                # Read header
                header = f.read(12)  # 4 bytes magic + 4 bytes version + 4 bytes compression
                if len(header) != 12:
                    raise ValueError("Invalid file header")

                magic, version, compression = struct.unpack('4sII', header)

                if magic != KVIZ_MAGIC:
                    raise ValueError(f"Invalid magic number: {magic}")

                if version != KVIZ_VERSION:
                    print(f"Warning: Preset version {version} may not be fully compatible")

                # Read data length
                data_length = struct.unpack('I', f.read(4))[0]

                # Read data
                data = f.read(data_length)
                if len(data) != data_length:
                    raise ValueError("Incomplete data")

                # Deserialize based on compression
                if compression == COMPRESSION_NONE:
                    preset_dict, _ = self._decode_compact_data(data)
                elif compression == COMPRESSION_GZIP:
                    json_data = gzip.decompress(data).decode('utf-8')
                    preset_dict = json.loads(json_data)
                elif compression == COMPRESSION_PICKLE:
                    pickle_data = gzip.decompress(data)
                    preset_dict = pickle.loads(pickle_data)
                else:
                    raise ValueError(f"Unknown compression level: {compression}")

                # Convert back to PresetInfo
                preset_info = PresetInfo(**preset_dict)
                preset_info.file_path = str(filepath)

                return preset_info

        except Exception as e:
            print(f"Error loading .kviz format from {filepath}: {e}")
            return None

    def capture_visualizer_state(self, visualizer) -> Dict[str, Any]:
        """Capture complete visualizer state"""
        try:
            print(f"🔍 [DEBUG] Capturing visualizer state...")
            current_waveform = getattr(visualizer, "current_waveform_name", None)
            print(f"🔍 [DEBUG] Current waveform name: {current_waveform}")

            # Only capture attributes that actually exist on the visualizer
            state = {}
            
            # Define the attributes we want to capture with their expected defaults
            # Only include attributes that should be part of presets (exclude runtime state)
            attributes_to_capture = {
                # Animation settings
                "animation_speed": 1.0,
                "audio_speed_boost": 1.0,
                # Waveform settings - CRITICAL: Include current waveform name
                "current_waveform_name": None,
                "waveform_style": 0,
                "current_waveform_style": 0,
                "waveform_scale": 1.0,
                # Effect settings
                "rotation_mode": 0,
                "pulse_enabled": True,
                "pulse_intensity": 1.0,
                "trail_intensity": 0.8,
                "glow_intensity": 0.9,
                "smoke_intensity": 0.5,
                # Symmetry settings
                "symmetry_mode": 0,
                "kaleidoscope_sections": 6,
                # Bounce settings
                "bounce_enabled": False,
                # Beat detection
                "beats_per_change": 16,
                # Warp settings
                "warp_intensity": 1.0,
                # Note: transitions_paused excluded - it's runtime state, not preset data
            }
            
            # Handle special cases for attributes with different names
            attribute_mappings = {
                "warp_first": "warp_first_enabled",
                "invert_rotation": "invert_rotation_direction", 
                "bounce_intensity": "bounce_intensity_multiplier",
            }
            
            # Capture attributes that exist
            for attr_name, default_value in attributes_to_capture.items():
                if hasattr(visualizer, attr_name):
                    state[attr_name] = getattr(visualizer, attr_name, default_value)
                    print(f"🔍 [DEBUG] Captured {attr_name} = {state[attr_name]}")
                else:
                    print(f"🔍 [DEBUG] Visualizer missing attribute: {attr_name}")
            
            # Handle mapped attributes
            for preset_name, visualizer_name in attribute_mappings.items():
                if hasattr(visualizer, visualizer_name):
                    state[preset_name] = getattr(visualizer, visualizer_name)
                    print(f"🔍 [DEBUG] Captured {preset_name} (mapped from {visualizer_name}) = {state[preset_name]}")
                else:
                    print(f"🔍 [DEBUG] Visualizer missing mapped attribute: {visualizer_name}")

            return state

        except Exception as e:
            print(f"Error capturing visualizer state: {e}")
            return {}

    def capture_palette_info(self, visualizer) -> Dict[str, Any]:
        """Capture current palette information"""
        try:
            palette_info = {
                "palette_mode": getattr(visualizer, 'palette_mode', 'auto'),
                "selected_palette_name": getattr(visualizer, 'selected_palette_name', None),
                "palette_speed": getattr(visualizer, 'palette_speed', 1.0),
                "color_cycle_speed": getattr(visualizer, 'color_cycle_speed', 1.0),
                "palette_transition_speed": getattr(visualizer, 'palette_transition_speed', 0.02),
                "color_transition_smoothness": getattr(visualizer, 'color_transition_smoothness', 0.1),
                "color_index": getattr(visualizer, 'color_index', 0),
                "color_time": getattr(visualizer, 'color_time', 0),
            }

            # Capture current palette colors if available
            if hasattr(visualizer, 'current_palette') and visualizer.current_palette:
                palette_info["current_palette_colors"] = visualizer.current_palette

            # Capture palette manager state if available
            if hasattr(visualizer, 'palette_manager') and visualizer.palette_manager:
                pm = visualizer.palette_manager
                if hasattr(pm, 'current_palette_info') and pm.current_palette_info:
                    palette_info["palette_details"] = {
                        "name": pm.current_palette_info.name,
                        "colors": pm.current_palette_info.colors,
                        "energy_level": pm.current_palette_info.energy_level,
                        "warmth": pm.current_palette_info.warmth,
                        "description": pm.current_palette_info.description,
                        "is_builtin": pm.current_palette_info.is_builtin,
                    }

            return palette_info

        except Exception as e:
            print(f"Error capturing palette info: {e}")
            return {}

    def capture_warp_map_info(self, visualizer) -> Dict[str, Any]:
        """Capture current warp map information including complete shader code"""
        try:
            print(f"🔍 [DEBUG] Starting warp map capture...")
            print(f"🔍 [DEBUG] Visualizer active_warp_map: {getattr(visualizer, 'active_warp_map', 'NOT_SET')}")
            print(f"🔍 [DEBUG] Visualizer active_warp_map_name: {getattr(visualizer, 'active_warp_map_name', 'NOT_SET')}")
            print(f"🔍 [DEBUG] Visualizer active_warp_map_index: {getattr(visualizer, 'active_warp_map_index', 'NOT_SET')}")

            warp_info = {
                "active_warp_map": getattr(visualizer, 'active_warp_map', 0),
                "active_warp_map_name": getattr(visualizer, 'active_warp_map_name', None),
                "active_warp_map_index": getattr(visualizer, 'active_warp_map_index', -1),
                "warp_intensity": getattr(visualizer, 'warp_intensity', 1.0),
                "warp_first_enabled": getattr(visualizer, 'warp_first_enabled', False),
            }

            # Capture warp map manager state if available
            if hasattr(visualizer, 'warp_map_manager') and visualizer.warp_map_manager:
                wmm = visualizer.warp_map_manager
                print(f"🔍 [DEBUG] Warp map manager found")
                print(f"🔍 [DEBUG] WMM current_warp_map: {wmm.current_warp_map.name if hasattr(wmm, 'current_warp_map') and wmm.current_warp_map else 'NOT_SET'}")

                # Priority 1: Try to get warp map by visualizer's active_warp_map_name first
                if hasattr(visualizer, 'active_warp_map_name') and visualizer.active_warp_map_name:
                    warp_map_name = visualizer.active_warp_map_name
                    print(f"🔍 [DEBUG] Trying to find warp map by name: {warp_map_name}")
                    if hasattr(wmm, 'warp_maps') and warp_map_name in wmm.warp_maps:
                        warp_map = wmm.warp_maps[warp_map_name]
                        print(f"🔍 [DEBUG] Found warp map in wmm.warp_maps")
                        print(f"🔍 [DEBUG] Shader code length: {len(warp_map.glsl_code) if warp_map.glsl_code else 0}")
                        print(f"🔍 [DEBUG] Shader code preview: {warp_map.glsl_code[:200] if warp_map.glsl_code else 'EMPTY'}...")
                        print(f"🔍 [DEBUG] COMPLETE WARP MAP SHADER CODE FOR CAPTURE (BY NAME):")
                        print(f"{'='*80}")
                        print(warp_map.glsl_code if warp_map.glsl_code else "NO SHADER CODE")
                        print(f"{'='*80}")

                        warp_info["current_warp_map"] = {
                            "name": warp_map.name,
                            "category": warp_map.category,
                            "description": warp_map.description,
                            "glsl_code": warp_map.glsl_code,  # Complete shader code
                            "complexity": warp_map.complexity,
                            "author": warp_map.author,
                            "version": warp_map.version,
                            "is_builtin": warp_map.is_builtin,
                        }
                        print(f"📝 Captured shader code for '{warp_map.name}' by name ({len(warp_map.glsl_code)} characters)")

                        # Also update the warp map manager's current_warp_map for consistency
                        print(f"🔍 [DEBUG] Updating wmm.current_warp_map for consistency")
                        wmm.current_warp_map = warp_map
                    else:
                        print(f"🔍 [DEBUG] Warp map '{warp_map_name}' not found in wmm.warp_maps")
                
                # Priority 2: Fallback to passthrough shader if no valid warp map found
                if "current_warp_map" not in warp_info:
                    print(f"🔍 [DEBUG] No valid warp map found - creating passthrough shader")
                    passthrough_shader = """// Basic passthrough warp map for preset compatibility
// This shader applies no transformation to the coordinates

// Required function for warp map compatibility - correct signature
vec2 get_pattern(vec2 pos, float t) {
    return vec2(0.0, 0.0);  // No warp displacement - passthrough
}"""
                    
                    warp_info["current_warp_map"] = {
                        "name": "passthrough",
                        "category": "basic",
                        "description": "Passthrough warp map - no transformation",
                        "glsl_code": passthrough_shader,
                        "complexity": 1,
                        "author": "KarmaViz",
                        "version": "1.0",
                        "is_builtin": True,
                    }
                    print(f"📝 Created passthrough shader for preset compatibility")

                # Get list of available warp maps for reference
                if hasattr(wmm, 'warp_map_names'):
                    warp_info["available_warp_maps"] = wmm.warp_map_names
                elif hasattr(wmm, 'warp_maps'):
                    warp_info["available_warp_maps"] = list(wmm.warp_maps.keys())

            # Capture shader compiler state if available
            if hasattr(visualizer, 'shader_compiler') and visualizer.shader_compiler:
                sc = visualizer.shader_compiler
                if hasattr(sc, 'active_warp_maps'):
                    warp_info["active_warp_maps"] = sc.active_warp_maps

                # Capture compiled shader information
                if hasattr(sc, 'current_shader_source'):
                    warp_info["compiled_shader_info"] = {
                        "has_compiled_shader": True,
                        "shader_length": len(sc.current_shader_source) if sc.current_shader_source else 0
                    }

            # Final verification
            if "current_warp_map" in warp_info:
                shader_code = warp_info["current_warp_map"].get("glsl_code", "")
                if shader_code and len(shader_code.strip()) > 0:
                    print(f"Successfully captured warp map with {len(shader_code)} character shader code")
                else:
                    print(f"Warning: Captured warp map but shader code is empty or missing")

            return warp_info

        except Exception as e:
            print(f"Error capturing warp map info: {e}")
            return {}

    def capture_waveform_info(self, visualizer) -> Dict[str, Any]:
        """Capture current waveform information including complete shader code"""
        try:
            print(f"🔍 [DEBUG] Starting waveform capture...")
            current_waveform_name = getattr(visualizer, 'current_waveform_name', None)
            print(f"🔍 [DEBUG] Current waveform name: {current_waveform_name}")

            waveform_info = {
                "current_waveform_name": current_waveform_name,
                "waveform_index": getattr(visualizer, 'waveform_index', -1),
            }

            # Capture waveform manager state if available
            wfm = None
            if hasattr(visualizer, 'waveform_manager') and visualizer.waveform_manager:
                wfm = visualizer.waveform_manager
                print(f"🔍 [DEBUG] Waveform manager found via visualizer.waveform_manager")
            elif hasattr(visualizer, 'shader_manager') and visualizer.shader_manager and hasattr(visualizer.shader_manager, 'waveform_manager'):
                wfm = visualizer.shader_manager.waveform_manager
                print(f"🔍 [DEBUG] Waveform manager found via visualizer.shader_manager.waveform_manager")

            if wfm:

                # Get current waveform info with complete shader code
                if current_waveform_name and hasattr(wfm, 'waveforms') and current_waveform_name in wfm.waveforms:
                    current_waveform = wfm.waveforms[current_waveform_name]
                    print(f"🔍 [DEBUG] Capturing waveform: {current_waveform.name}")
                    print(f"🔍 [DEBUG] Shader code length: {len(current_waveform.glsl_code) if current_waveform.glsl_code else 0}")
                    print(f"🔍 [DEBUG] Shader code preview: {current_waveform.glsl_code[:200] if current_waveform.glsl_code else 'EMPTY'}...")
                    print(f"🔍 [DEBUG] COMPLETE SHADER CODE FOR CAPTURE:")
                    print(f"{'='*80}")
                    print(current_waveform.glsl_code if current_waveform.glsl_code else "NO SHADER CODE")
                    print(f"{'='*80}")

                    waveform_info["current_waveform"] = {
                        "name": current_waveform.name,
                        "category": current_waveform.category,
                        "description": current_waveform.description,
                        "glsl_code": current_waveform.glsl_code,  # Complete shader code
                        "complexity": current_waveform.complexity,
                        "author": current_waveform.author,
                        "version": current_waveform.version,
                        "is_builtin": current_waveform.is_builtin,
                    }

                    # Verify shader code is not empty
                    if not current_waveform.glsl_code or len(current_waveform.glsl_code.strip()) == 0:
                        print(f"Warning: Waveform '{current_waveform.name}' has empty shader code")
                    else:
                        print(f"📝 Captured waveform shader code for '{current_waveform.name}' ({len(current_waveform.glsl_code)} characters)")
                else:
                    print(f"🔍 [DEBUG] Waveform '{current_waveform_name}' not found in waveform manager")
                    if hasattr(wfm, 'waveforms'):
                        print(f"🔍 [DEBUG] Available waveforms: {list(wfm.waveforms.keys())}")

                # Get list of available waveforms for reference
                if hasattr(wfm, 'waveform_names'):
                    waveform_info["available_waveforms"] = wfm.waveform_names
            else:
                print(f"🔍 [DEBUG] No waveform manager found")

            return waveform_info

        except Exception as e:
            print(f"Error capturing waveform info: {e}")
            return {}

    def capture_audio_settings(self, visualizer) -> Dict[str, Any]:
        """Capture audio processing settings"""
        try:
            audio_settings = {
                "chunk_size": getattr(visualizer, 'chunk_size', 512),
                "sample_rate": getattr(visualizer, 'sample_rate', 80000),
                "channels": getattr(visualizer, 'channels', 1),
                "audio_format": getattr(visualizer, 'audio_format', 'float32'),
            }

            return audio_settings

        except Exception as e:
            print(f"Error capturing audio settings: {e}")
            return {}

    def capture_effect_settings(self, visualizer) -> Dict[str, Any]:
        """Capture effect-specific settings"""
        try:
            effect_settings = {
                # Anti-aliasing
                "msaa_samples": getattr(visualizer, 'msaa_samples', 0),
                "fxaa_enabled": getattr(visualizer, 'fxaa_enabled', False),

                # Performance settings
                "vsync_enabled": getattr(visualizer, 'vsync_enabled', True),
                "frame_limit": getattr(visualizer, 'frame_limit', None),

                # Rendering settings
                "render_quality": getattr(visualizer, 'render_quality', 'high'),
                "texture_filtering": getattr(visualizer, 'texture_filtering', True),
            }

            return effect_settings

        except Exception as e:
            print(f"Error capturing effect settings: {e}")
            return {}

    def save_preset(self, visualizer, name: str, description: str = "",
                   author: str = "User", tags: List[str] = None) -> bool:
        """Save current visualizer state as a preset"""
        try:
            # Sanitize filename
            safe_name = self._sanitize_filename(name)

            # Capture all state information
            print(f"🔍 [DEBUG] About to capture waveform info...")
            waveform_info = self.capture_waveform_info(visualizer)
            print(f"🔍 [DEBUG] Waveform info captured: {list(waveform_info.keys()) if waveform_info else 'EMPTY'}")

            preset_info = PresetInfo(
                name=name,
                description=description,
                created_date=datetime.now().isoformat(),
                author=author,
                version="1.0",
                visualizer_settings=self.capture_visualizer_state(visualizer),
                palette_info=self.capture_palette_info(visualizer),
                warp_map_info=self.capture_warp_map_info(visualizer),
                waveform_info=waveform_info,
                audio_settings=self.capture_audio_settings(visualizer),
                effect_settings=self.capture_effect_settings(visualizer),
                tags=tags or [],
            )

            # Save to file in .kviz format
            filepath = self.presets_dir / f"{safe_name}{KVIZ_EXTENSION}"
            preset_info.file_path = str(filepath)

            # Save in compact .kviz format
            success = self._save_kviz_format(preset_info, filepath)

            if success:
                # Add to internal storage
                self.presets[name] = preset_info

                # Get file size for comparison
                file_size = filepath.stat().st_size
                print(f"Preset '{name}' saved successfully to {filepath} ({file_size} bytes)")
                return True
            else:
                print(f"Failed to save preset '{name}' in .kviz format")
                return False

        except Exception as e:
            print(f"Error saving preset '{name}': {e}")
            return False

    def save_quick_preset(self, visualizer, slot: int) -> bool:
        """Save current state as a quick preset (Ctrl+0-9)
        
        Args:
            visualizer: The visualizer instance
            slot: Quick preset slot (0-9)
            
        Returns:
            True if successful
        """
        if not (0 <= slot <= 9):
            print(f"Invalid quick preset slot: {slot}")
            return False

        try:
            name = f"Quick Preset {slot}"
            description = f"Quick preset saved to slot {slot} on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

            # Capture all state information
            print(f"🔍 [DEBUG] About to capture waveform info for quick preset...")
            waveform_info = self.capture_waveform_info(visualizer)
            print(f"🔍 [DEBUG] Quick preset waveform info captured: {list(waveform_info.keys()) if waveform_info else 'EMPTY'}")

            preset_info = PresetInfo(
                name=name,
                description=description,
                created_date=datetime.now().isoformat(),
                author="User",
                version="1.0",
                visualizer_settings=self.capture_visualizer_state(visualizer),
                palette_info=self.capture_palette_info(visualizer),
                warp_map_info=self.capture_warp_map_info(visualizer),
                waveform_info=waveform_info,  # Add missing waveform_info
                audio_settings=self.capture_audio_settings(visualizer),
                effect_settings=self.capture_effect_settings(visualizer),
                tags=["quick"],
            )

            # Save to quick presets directory
            filepath = self.quick_presets_dir / f"quick_{slot}{KVIZ_EXTENSION}"
            preset_info.file_path = str(filepath)

            # Save in compact .kviz format
            success = self._save_kviz_format(preset_info, filepath)

            if success:
                print(f"Quick preset {slot} saved successfully")
                return True
            else:
                print(f"Failed to save quick preset {slot}")
                return False

        except Exception as e:
            print(f"Error saving quick preset {slot}: {e}")
            return False

    def load_quick_preset(self, visualizer, slot: int) -> bool:
        """Load a quick preset (0-9)
        
        Args:
            visualizer: The visualizer instance
            slot: Quick preset slot (0-9)
            
        Returns:
            True if successful
        """
        if not (0 <= slot <= 9):
            print(f"Invalid quick preset slot: {slot}")
            return False

        try:
            filepath = self.quick_presets_dir / f"quick_{slot}{KVIZ_EXTENSION}"

            if not filepath.exists():
                print(f"Quick preset {slot} not found")
                return False

            # Load the preset
            preset_info = self._load_kviz_format(filepath)
            if preset_info is None:
                print(f"Failed to load quick preset {slot}")
                return False

            # Apply the preset to the visualizer (shader-only mode)
            success = self.apply_preset(visualizer, preset_info)

            if success:
                print(f"Quick preset {slot} loaded successfully")
                return True
            else:
                print(f"Failed to apply quick preset {slot}")
                return False

        except Exception as e:
            print(f"Error loading quick preset {slot}: {e}")
            return False

    def quick_preset_exists(self, slot: int) -> bool:
        """Check if a quick preset exists
        
        Args:
            slot: Quick preset slot (0-9)
            
        Returns:
            True if the preset exists
        """
        if not (0 <= slot <= 9):
            return False

        filepath = self.quick_presets_dir / f"quick_{slot}{KVIZ_EXTENSION}"
        return filepath.exists()

    def _sanitize_filename(self, name: str) -> str:
        """Convert a preset name to a safe filename"""
        # Replace spaces with underscores, convert to lowercase, and remove unsafe characters
        safe_name = name.lower().replace(' ', '_')

        # Remove or replace unsafe characters
        unsafe_chars = ['/', '\\', ':', '*', '?', '"', '<', '>', '|']
        for char in unsafe_chars:
            safe_name = safe_name.replace(char, '_')

        # Remove multiple consecutive underscores
        while '__' in safe_name:
            safe_name = safe_name.replace('__', '_')

        # Remove leading/trailing underscores
        safe_name = safe_name.strip('_')

        # Ensure it's not empty
        if not safe_name:
            safe_name = f'preset_{int(time.time())}'

        return safe_name

    def load_preset(self, name: str, visualizer) -> bool:
        """Load a preset and apply it to the visualizer"""
        try:
            if name not in self.presets:
                print(f"Preset '{name}' not found")
                return False

            preset = self.presets[name]

            print(f"Loading preset '{name}' (shader-only mode)...")

            # Apply only shader code from the preset
            success = self.apply_preset(visualizer, preset)
            if not success:
                return False

            print(f"Preset '{name}' loaded successfully!")
            return True

        except Exception as e:
            print(f"Error loading preset '{name}': {e}")
            return False

    def apply_preset(self, visualizer, preset_info: PresetInfo) -> bool:
        """Apply only shader code from a preset (waveform and warp map shaders)
        
        This is a minimal preset restoration that only restores shader code,
        avoiding potential issues with other state restoration.
        """
        try:
            print(f"Applying shader-only preset '{preset_info.name}'...")

            # Only apply warp map shader code
            if preset_info.warp_map_info and 'current_warp_map' in preset_info.warp_map_info:
                self._apply_warp_map_settings(visualizer, preset_info.warp_map_info)

            # Only apply waveform shader code
            if preset_info.waveform_info and 'current_waveform' in preset_info.waveform_info:
                self._apply_waveform_settings(visualizer, preset_info.waveform_info)

            # Apply visualizer settings
            self._apply_visualizer_settings(visualizer, preset_info.visualizer_settings)
            # Apply palette settings
            self._apply_palette_settings(visualizer, preset_info.palette_info)

            # Apply effect settings
            self._apply_effect_settings(visualizer, preset_info.effect_settings)


            print(f"Shader-only preset '{preset_info.name}' applied successfully!")
            return True

        except Exception as e:
            print(f"Error applying shader-only preset '{preset_info.name}': {e}")
            return False

    def check_and_apply_shader_compilations(self, visualizer) -> int:
        """Check for completed shader compilations from preset loading and apply them.
        
        Note: With the new synchronous compilation system, this method is no longer needed
        as shaders are compiled immediately when presets are loaded.
        
        Args:
            visualizer: The visualizer instance
            
        Returns:
            Number of shader compilations applied (always 0 now)
        """
        # With synchronous compilation, no async checking is needed
        return 0

    def _apply_visualizer_settings(self, visualizer, settings: Dict[str, Any]):
        """Apply visualizer settings to the visualizer instance"""
        try:
            print(f"🔍 [DEBUG] Applying visualizer settings...")
            print(f"🔍 [DEBUG] Settings keys: {list(settings.keys())}")
            waveform_name = settings.get('current_waveform_name')
            print(f"🔍 [DEBUG] Waveform name in settings: {waveform_name}")

            # Handle mapped attributes (preset name -> visualizer attribute name)
            attribute_mappings = {
                "warp_first": "warp_first_enabled",
                "invert_rotation": "invert_rotation_direction",
                "bounce_intensity": "bounce_intensity_multiplier",
            }

            # Skip runtime-only attributes that shouldn't be restored from presets
            runtime_only_attributes = {
                'transitions_paused',  # User-specific runtime state
                'fps',                 # System-specific setting
                'width',               # Window-specific setting
                'height',              # Window-specific setting
                'fullscreen',          # User-specific runtime state
                'selected_fullscreen_res',  # System-specific setting
                'mouse_enabled',       # User-specific runtime state
                'deformation_time',    # Internal timing state
                'waveform_length',     # Internal buffer size
                'beat_sensitivity',    # May not exist on all versions
                'gpu_waveform_enabled', # Internal rendering state
            }

            for key, value in settings.items():
                # Skip runtime-only attributes
                if key in runtime_only_attributes:
                    print(f"🔍 [DEBUG] Skipping runtime-only attribute: {key}")
                    continue
                    
                # Check if this is a mapped attribute
                actual_attr_name = attribute_mappings.get(key, key)
                
                if hasattr(visualizer, actual_attr_name):
                    print(f"🔍 [DEBUG] Setting {actual_attr_name} = {value}")
                    setattr(visualizer, actual_attr_name, value)
                else:
                    print(f"🔍 [DEBUG] Visualizer has no attribute: {actual_attr_name} (from {key})")

            # Special handling for waveform switching
            if waveform_name and hasattr(visualizer, 'select_waveform'):
                print(f"🔍 [DEBUG] Switching to waveform: {waveform_name}")
                try:
                    visualizer.select_waveform(waveform_name)
                    print(f"🔍 [DEBUG] Successfully switched to waveform: {waveform_name}")
                except Exception as wf_error:
                    print(f"🔍 [DEBUG] Failed to switch waveform: {wf_error}")

            # Force update shader uniforms for visual settings
            if hasattr(visualizer, 'program') and visualizer.program:
                # Update common shader uniforms
                uniform_mappings = {
                    'symmetry_mode': 'symmetry_mode',
                    'rotation_mode': 'rotation_mode',
                    'brightness': 'brightness',
                    'contrast': 'contrast',
                    'saturation': 'saturation',
                    'hue_shift': 'hue_shift',
                    'pulse_intensity': 'pulse_intensity',
                    'trail_intensity': 'trail_intensity',
                    'glow_intensity': 'glow_intensity',
                    'glow_radius': 'glow_radius',
                    'smoke_intensity': 'smoke_intensity',
                    'waveform_scale': 'waveform_scale',
                    'rotation_speed': 'rotation_speed',
                    'rotation_amplitude': 'rotation_amplitude',
                    'warp_intensity': 'warp_intensity'
                }

                for setting_key, uniform_name in uniform_mappings.items():
                    if setting_key in settings:
                        try:
                            if uniform_name in visualizer.program:
                                visualizer.program[uniform_name].value = settings[setting_key]
                        except Exception as uniform_error:
                            # Some uniforms might not exist in all shaders
                            pass

            # Force palette update if colors changed
            if 'current_palette' in settings and hasattr(visualizer, 'update_palette_texture'):
                try:
                    visualizer.update_palette_texture()
                except Exception:
                    pass

            print(f"   Applied visualizer settings with shader updates")

        except Exception as e:
            print(f"   Error applying visualizer settings: {e}")

    def _apply_palette_settings(self, visualizer, palette_info: Dict[str, Any]):
        """Apply palette settings to the visualizer instance"""
        try:
            # Apply basic palette settings
            for key, value in palette_info.items():
                if hasattr(visualizer, key) and key != 'palette_details' and key != 'current_palette_colors':
                    setattr(visualizer, key, value)

            # Apply current palette colors if available
            if 'current_palette_colors' in palette_info:
                visualizer.current_palette = palette_info['current_palette_colors']

                # Force palette texture update
                if hasattr(visualizer, 'update_palette_texture'):
                    try:
                        visualizer.update_palette_texture()
                    except Exception as e:
                        print(f"   Warning: Could not update palette texture: {e}")

            # Apply specific palette if available
            if 'palette_details' in palette_info and hasattr(visualizer, 'palette_manager'):
                palette_details = palette_info['palette_details']

                # Try to find and set the specific palette
                if palette_details['name'] in visualizer.palette_manager.palettes:
                    visualizer.palette_manager.set_current_palette(palette_details['name'])
                else:
                    # Create a temporary palette from the saved data
                    from modules.palette_manager import PaletteInfo
                    temp_palette = PaletteInfo(
                        name=palette_details['name'],
                        colors=palette_details['colors'],
                        energy_level=palette_details['energy_level'],
                        warmth=palette_details['warmth'],
                        description=palette_details['description'],
                        is_builtin=False  # Mark as non-builtin since it's from preset
                    )
                    visualizer.palette_manager.palettes[palette_details['name']] = temp_palette
                    visualizer.palette_manager.set_current_palette(palette_details['name'])

            print(f"   Applied palette settings")

        except Exception as e:
            print(f"   Error applying palette settings: {e}")

    def _apply_warp_map_settings(self, visualizer, warp_info: Dict[str, Any]):
        """Apply warp map settings to the visualizer instance"""
        try:
            print(f"🔍 [DEBUG] Starting warp map restoration...")
            print(f"🔍 [DEBUG] Warp info keys: {list(warp_info.keys())}")
            print(f"🔍 [DEBUG] Current warp map in info: {'current_warp_map' in warp_info}")

            # Apply basic warp settings
            for key, value in warp_info.items():
                if hasattr(visualizer, key) and key not in ['current_warp_map', 'available_warp_maps', 'active_warp_maps', 'compiled_shader_info']:
                    print(f"🔍 [DEBUG] Setting visualizer.{key} = {value}")
                    setattr(visualizer, key, value)

            # Apply specific warp map if available
            if 'current_warp_map' in warp_info and hasattr(visualizer, 'warp_map_manager'):
                warp_details = warp_info['current_warp_map']
                print(f"🔍 [DEBUG] Restoring warp map: {warp_details.get('name', 'UNKNOWN')}")

            if "current_warp_map" not in warp_info:
                print(f"   No current_warp_map in preset data")
                return

            warp_details = warp_info["current_warp_map"]
            warp_name = warp_details.get("name", "UNKNOWN")
            shader_code = warp_details.get("glsl_code", "")

            print(f"🔍 [DEBUG] Restoring warp map shader: {warp_name}")
            print(
                f"🔍 [DEBUG] Shader code length: {len(shader_code) if shader_code else 0}"
            )

            if not shader_code or len(shader_code.strip()) == 0:
                print(f"   Warning: No shader code for warp map '{warp_name}'")
                return

            if (
                not hasattr(visualizer, "warp_map_manager")
                or not visualizer.warp_map_manager
            ):
                print(f"   Warning: No warp map manager available")
                return

            # Update or create the warp map with the shader code
            if warp_name in visualizer.warp_map_manager.warp_maps:
                # Update existing warp map
                existing_warp = visualizer.warp_map_manager.warp_maps[warp_name]
                existing_warp.glsl_code = shader_code
                print(
                    f"   📝 Updated existing warp map '{warp_name}' with preset shader code"
                )
            else:
                # Create new warp map (this shouldn't normally happen, but handle it)
                from modules.warp_map_manager import WarpMapInfo

                new_warp = WarpMapInfo(
                    name=warp_name,
                    category=warp_details.get("category", "basic"),
                    glsl_code=shader_code,
                    description=warp_details.get(
                        "description", f"Restored from preset"
                    ),
                    complexity=warp_details.get("complexity", "medium"),
                )
                visualizer.warp_map_manager.warp_maps[warp_name] = new_warp
                print(
                    f"   📝 Created new warp map '{warp_name}' from preset shader code"
                )

            # Set as current warp map
            if hasattr(visualizer.warp_map_manager, "set_current_warp_map"):
                visualizer.warp_map_manager.set_current_warp_map(warp_name)
            else:
                visualizer.warp_map_manager.current_warp_map = (
                    visualizer.warp_map_manager.warp_maps[warp_name]
                )

            # Update visualizer state and recompile warp map shader
            visualizer.active_warp_map_name = warp_name
            
            # Recompile the warp map shader with the new warp map
            try:
                visualizer.select_random_warp_map(warp_name)
                print(f"   ✓ Warp map shader recompiled for '{warp_name}'")
            except Exception as e:
                print(f"   ✗ Failed to recompile warp map shader: {e}")

            print(f"   Applied warp map restoration for '{warp_name}'")

        except Exception as e:
            print(f"   Error applying warp map shader-only: {e}")
            

            

    def _apply_effect_settings(self, visualizer, effect_settings: Dict[str, Any]):
        """Apply effect settings to the visualizer instance"""
        try:
            for key, value in effect_settings.items():
                if hasattr(visualizer, key):
                    setattr(visualizer, key, value)

            print(f"   Applied effect settings")

        except Exception as e:
            print(f"   Error applying effect settings: {e}")

    def _apply_warp_map_settings(self, visualizer, warp_info: Dict[str, Any]):
        """Apply only the warp map shader code from preset data"""
        try:
            print(f"🔍 [DEBUG] Starting warp map shader-only restoration...")
            if "current_warp_map" not in warp_info:
                print(f"   No current_warp_map in preset data")
                return

            warp_details = warp_info["current_warp_map"]
            warp_name = warp_details.get("name", "UNKNOWN")
            shader_code = warp_details.get("glsl_code", "")

            print(f"🔍 [DEBUG] Restoring warp map shader: {warp_name}")
            print(
                f"🔍 [DEBUG] Shader code length: {len(shader_code) if shader_code else 0}"
            )

            if not shader_code or len(shader_code.strip()) == 0:
                print(f"   Warning: No shader code for warp map '{warp_name}'")
                return

            if (
                not hasattr(visualizer, "warp_map_manager")
                or not visualizer.warp_map_manager
            ):
                print(f"   Warning: No warp map manager available")
                return

            # Update or create the warp map with the shader code
            if warp_name in visualizer.warp_map_manager.warp_maps:
                # Update existing warp map
                existing_warp = visualizer.warp_map_manager.warp_maps[warp_name]
                existing_warp.glsl_code = shader_code
                print(
                    f"   📝 Updated existing warp map '{warp_name}' with preset shader code"
                )
            else:
                # Create new warp map (this shouldn't normally happen, but handle it)
                from modules.warp_map_manager import WarpMapInfo

                new_warp = WarpMapInfo(
                    name=warp_name,
                    category=warp_details.get("category", "basic"),
                    glsl_code=shader_code,
                    description=warp_details.get(
                        "description", f"Restored from preset"
                    ),
                    complexity=warp_details.get("complexity", "medium"),
                )
                visualizer.warp_map_manager.warp_maps[warp_name] = new_warp
                print(
                    f"   📝 Created new warp map '{warp_name}' from preset shader code"
                )

            # Set as current warp map
            if hasattr(visualizer.warp_map_manager, "set_current_warp_map"):
                visualizer.warp_map_manager.set_current_warp_map(warp_name)
            else:
                visualizer.warp_map_manager.current_warp_map = (
                    visualizer.warp_map_manager.warp_maps[warp_name]
                )

            # Update visualizer state and recompile warp map shader
            visualizer.active_warp_map_name = warp_name
            
            # Recompile the warp map shader with the new warp map
            try:
                visualizer.select_random_warp_map(warp_name)
                print(f"   ✓ Warp map shader recompiled for '{warp_name}'")
            except Exception as e:
                print(f"   ✗ Failed to recompile warp map shader: {e}")

            print(f"   Applied warp map restoration for '{warp_name}'")

        except Exception as e:
            print(f"   Error applying warp map shader-only: {e}")
            
            

    def _apply_waveform_settings(self, visualizer, waveform_info: Dict[str, Any]):
        """Apply only the waveform shader code from preset data"""
        try:
            print(f"🔍 [DEBUG] Starting waveform shader-only restoration...")

            if 'current_waveform' not in waveform_info:
                print(f"   No current_waveform in preset data")
                return

            waveform_details = waveform_info['current_waveform']
            waveform_name = waveform_details.get('name', 'UNKNOWN')
            shader_code = waveform_details.get('glsl_code', '')

            print(f"🔍 [DEBUG] Restoring waveform shader: {waveform_name}")
            print(f"🔍 [DEBUG] Shader code length: {len(shader_code) if shader_code else 0}")

            if not shader_code or len(shader_code.strip()) == 0:
                print(f"   Warning: No shader code for waveform '{waveform_name}'")
                return

            # Find waveform manager
            wfm = None
            if hasattr(visualizer, 'waveform_manager') and visualizer.waveform_manager:
                wfm = visualizer.waveform_manager
            elif hasattr(visualizer, 'shader_manager') and visualizer.shader_manager and hasattr(visualizer.shader_manager, 'waveform_manager'):
                wfm = visualizer.shader_manager.waveform_manager

            if not wfm:
                print(f"   Warning: No waveform manager available")
                return

            # Update or create the waveform with the shader code
            if waveform_name in wfm.waveforms:
                # Update existing waveform
                existing_waveform = wfm.waveforms[waveform_name]
                existing_waveform.glsl_code = shader_code
                print(f"   📝 Updated existing waveform '{waveform_name}' with preset shader code")
            else:
                # Create new waveform (this shouldn't normally happen, but handle it)
                from modules.waveform_manager import WaveformInfo
                new_waveform = WaveformInfo(
                    name=waveform_name,
                    glsl_code=shader_code,
                    description=waveform_details.get('description', f'Restored from preset'),
                    complexity=waveform_details.get('complexity', 'medium')
                )
                wfm.waveforms[waveform_name] = new_waveform
                print(f"   📝 Created new waveform '{waveform_name}' from preset shader code")

            # Set as current waveform
            if hasattr(wfm, 'set_current_waveform'):
                wfm.set_current_waveform(waveform_name)
            else:
                wfm.current_waveform = wfm.waveforms[waveform_name]

            # Update visualizer state
            visualizer.current_waveform_name = waveform_name

            # Recompile main shader with new waveform (no warp maps in main shader anymore)
            if hasattr(visualizer, 'shader_manager') and visualizer.shader_manager:
                print(f"   Recompiling main shader for waveform '{waveform_name}'")
                try:
                    # Build main shader without warp maps
                    fragment_shader_with_waveform = (
                        visualizer.shader_manager.build_full_fragment_shader(
                            waveform_name=waveform_name,
                            warp_map_name=None,  # No warp map in main shader
                        )
                    )
                    from shaders.shaders import VERTEX_SHADER
                    new_program = visualizer.ctx.program(
                        vertex_shader=VERTEX_SHADER,
                        fragment_shader=fragment_shader_with_waveform,
                    )
                    
                    # Replace old program
                    if hasattr(visualizer, 'program') and visualizer.program:
                        visualizer.program.release()
                    visualizer.program = new_program
                    
                    # Update VAO
                    visualizer.vao = visualizer.ctx.vertex_array(
                        visualizer.program,
                        [(visualizer.vbo, "2f 2f", "in_position", "in_texcoord")],
                    )
                    
                    # Initialize critical uniforms to prevent errors
                    try:
                        if "animation_speed" in visualizer.program:
                            visualizer.program["animation_speed"].value = getattr(visualizer, 'animation_speed', 1.0)
                        if "time" in visualizer.program:
                            visualizer.program["time"].value = getattr(visualizer, 'time', 0.0)
                        if "use_warp_coords" in visualizer.program:
                            has_warp = hasattr(visualizer, 'active_warp_map_name') and visualizer.active_warp_map_name
                            visualizer.program["use_warp_coords"].value = has_warp
                    except Exception as uniform_error:
                        print(f"   Warning: Could not initialize uniforms: {uniform_error}")
                    
                    print(f"   ✓ Main shader recompiled for waveform '{waveform_name}'")
                except Exception as e:
                    print(f"   ✗ Failed to recompile main shader: {e}")

            print(f"   Applied waveform shader-only restoration for '{waveform_name}'")

        except Exception as e:
            print(f"   Error applying waveform shader-only: {e}")
            
            

    def load_all_presets(self):
        """Load all presets from the presets directory (.kviz and legacy .json)"""
        try:
            self.presets.clear()
            kviz_count = 0

            # Load .kviz files (new format)
            for preset_file in self.presets_dir.glob("*.kviz"):
                preset_info = self._load_kviz_format(preset_file)
                if preset_info:
                    self.presets[preset_info.name] = preset_info
                    kviz_count += 1

            # Report loading results
            total_presets = len(self.presets)
            if kviz_count > 0:
                print(f" Loaded {total_presets} .kviz presets from {self.presets_dir}")
            else:
                print(f" Loaded {total_presets} presets from {self.presets_dir}")

        except Exception as e:
            print(f"Error loading presets: {e}")

    def get_preset_names(self) -> List[str]:
        """Get list of all preset names"""
        return list(self.presets.keys())

    def list_presets(self, directory=None) -> List[tuple]:
        """Get list of all presets as (filepath, preset_info) tuples"""
        result = []
        for preset_info in self.presets.values():
            if preset_info.file_path:
                result.append((preset_info.file_path, preset_info))
        return result

    def get_preset_info(self, name: str) -> Optional[PresetInfo]:
        """Get preset information by name"""
        return self.presets.get(name)

    def delete_preset(self, name: str) -> bool:
        """Delete a preset"""
        try:
            if name not in self.presets:
                print(f"Preset '{name}' not found")
                return False

            preset = self.presets[name]

            # Remove file
            if preset.file_path and os.path.exists(preset.file_path):
                os.remove(preset.file_path)

            # Remove from internal storage
            del self.presets[name]

            print(f"Preset '{name}' deleted successfully")
            return True

        except Exception as e:
            print(f"Error deleting preset '{name}': {e}")
            return False

    def export_preset(self, name: str, export_path: str) -> bool:
        """Export a preset to a specific file path (.kviz format)"""
        try:
            if name not in self.presets:
                print(f"Preset '{name}' not found")
                return False

            preset = self.presets[name]
            export_path_obj = Path(export_path)

            # Ensure .kviz extension
            if export_path_obj.suffix.lower() != KVIZ_EXTENSION:
                export_path_obj = export_path_obj.with_suffix(KVIZ_EXTENSION)

            # Export in .kviz format
            success = self._save_kviz_format(preset, export_path_obj)

            if success:
                file_size = export_path_obj.stat().st_size
                print(f"Preset '{name}' exported to {export_path_obj} ({file_size} bytes)")
                return True
            else:
                print(f"Failed to export preset '{name}' to {export_path_obj}")
                return False

        except Exception as e:
            print(f"Error exporting preset '{name}': {e}")
            return False

    def import_preset(self, import_path: str) -> bool:
        """Import a preset from a .kviz file"""
        try:
            import_path_obj = Path(import_path)

            # Only support .kviz format
            if import_path_obj.suffix.lower() != KVIZ_EXTENSION:
                print(f"Only .kviz preset files are supported. Got: {import_path_obj.suffix}")
                return False

            # Load .kviz format
            preset_info = self._load_kviz_format(import_path_obj)
            if not preset_info:
                print(f"Failed to load .kviz preset from {import_path}")
                return False

            # Save to presets directory in .kviz format
            safe_name = self._sanitize_filename(preset_info.name)
            new_filepath = self.presets_dir / f"{safe_name}{KVIZ_EXTENSION}"
            preset_info.file_path = str(new_filepath)

            # Save in .kviz format
            success = self._save_kviz_format(preset_info, new_filepath)

            if success:
                # Add to internal storage
                self.presets[preset_info.name] = preset_info

                file_size = new_filepath.stat().st_size
                print(f"Preset '{preset_info.name}' imported successfully ({file_size} bytes)")
                return True
            else:
                print(f"Failed to save imported preset '{preset_info.name}'")
                return False

        except Exception as e:
            print(f"Error importing preset from {import_path}: {e}")
            return False

    def _force_visual_refresh(self, visualizer):
        """Force a complete visual refresh after applying preset"""
        try:
            print(f"🔍 [DEBUG] Starting forced visual refresh...")
            print(f"🔍 [DEBUG] Current program: {getattr(visualizer, 'program', 'NOT_SET')}")
            print(f"🔍 [DEBUG] Current warp map: {getattr(visualizer, 'active_warp_map_name', 'NOT_SET')}")
            print(f"🔍 [DEBUG] Current waveform: {getattr(visualizer, 'current_waveform_name', 'NOT_SET')}")
            # Force shader recompilation with new synchronous system
            # The shaders should already be compiled by the preset loading process,
            # but we can force a refresh if needed
            print(f"   Shaders compiled synchronously during preset loading - no additional compilation needed")

            # Force palette texture update
            if hasattr(visualizer, 'update_palette_texture'):
                try:
                    visualizer.update_palette_texture()
                    print(f"   Forced palette texture update")
                except Exception as palette_error:
                    print(f"   Warning: Could not update palette texture: {palette_error}")

            # Force waveform update if needed
            if hasattr(visualizer, 'current_waveform_name') and hasattr(visualizer, 'waveform_manager'):
                try:
                    if visualizer.current_waveform_name in visualizer.waveform_manager.waveforms:
                        # Force waveform recompilation
                        if hasattr(visualizer, 'select_waveform'):
                            visualizer.select_waveform(visualizer.current_waveform_name)
                        print(f"   Forced waveform update")
                except Exception as waveform_error:
                    print(f"   Warning: Could not update waveform: {waveform_error}")

            # Update all shader uniforms with current values
            if hasattr(visualizer, 'program') and visualizer.program:
                try:
                    # Common uniforms that need updating
                    uniform_updates = {
                        'brightness': getattr(visualizer, 'brightness', 1.0),
                        'contrast': getattr(visualizer, 'contrast', 1.0),
                        'saturation': getattr(visualizer, 'saturation', 1.0),
                        'hue_shift': getattr(visualizer, 'hue_shift', 0.0),
                        'pulse_intensity': getattr(visualizer, 'pulse_intensity', 1.0),
                        'trail_intensity': getattr(visualizer, 'trail_intensity', 0.5),
                        'glow_intensity': getattr(visualizer, 'glow_intensity', 0.5),
                        'glow_radius': getattr(visualizer, 'glow_radius', 2.0),
                        'smoke_intensity': getattr(visualizer, 'smoke_intensity', 0.0),
                        'waveform_scale': getattr(visualizer, 'waveform_scale', 1.0),
                        'waveform_enabled': getattr(visualizer, 'gpu_waveform_enabled', True),  # CRITICAL: Map gpu_waveform_enabled to waveform_enabled uniform
                        'waveform_length': getattr(visualizer, 'waveform_length', 1024),
                        'rotation_speed': getattr(visualizer, 'rotation_speed', 1.0),
                        'rotation_amplitude': getattr(visualizer, 'rotation_amplitude', 1.0),
                        'warp_intensity': getattr(visualizer, 'warp_intensity', 1.0),
                        'symmetry_mode': getattr(visualizer, 'symmetry_mode', 0),
                        'rotation_mode': getattr(visualizer, 'rotation_mode', 0)
                    }

                    updated_count = 0
                    for uniform_name, value in uniform_updates.items():
                        try:
                            if uniform_name in visualizer.program:
                                visualizer.program[uniform_name].value = value
                                if uniform_name == 'waveform_enabled':
                                    print(f"   🔍 [DEBUG] ⭐ CRITICAL: Updated shader uniform {uniform_name} = {value}")
                                updated_count += 1
                        except Exception:
                            pass  # Some uniforms might not exist in current shader

                    if updated_count > 0:
                        print(f"   Updated {updated_count} shader uniforms")

                except Exception as uniform_error:
                    print(f"   Warning: Could not update shader uniforms: {uniform_error}")

            # Force immediate visual update by clearing any cached states
            if hasattr(visualizer, 'force_update'):
                try:
                    visualizer.force_update()
                except Exception:
                    pass

            print(f"   ✨ Forced complete visual refresh")

        except Exception as e:
            print(f"   Error during visual refresh: {e}")
