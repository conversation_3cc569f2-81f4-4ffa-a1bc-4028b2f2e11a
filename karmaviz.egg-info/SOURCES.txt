LICENSE.md
README.md
pyproject.toml
setup.py
config/constants.py
karmaviz.egg-info/PKG-INFO
karmaviz.egg-info/SOURCES.txt
karmaviz.egg-info/dependency_links.txt
karmaviz.egg-info/entry_points.txt
karmaviz.egg-info/requires.txt
karmaviz.egg-info/top_level.txt
modules/__init__.py
modules/audio_handler.py
modules/benchmark.py
modules/color_ops.c
modules/config_menu_qt.py
modules/glsl_syntax_highlighter.py
modules/line_numbered_editor.py
modules/logging_config.py
modules/palette_manager.py
modules/preset_manager.py
modules/preset_manager_widget.py
modules/rotation_renderer.py
modules/shader_compiler.py
modules/shader_compiler_backup.py
modules/shader_error_parser.py
modules/shader_manager.py
modules/warp_map_editor.py
modules/warp_map_manager.py
modules/waveform_editor.py
modules/waveform_manager.py
shaders/__init__.py
shaders/fxaa.glsl
shaders/shaders.py