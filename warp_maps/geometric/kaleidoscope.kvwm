KVWM      kaleidoscope	   geometricD   Creates radial symmetry patterns like looking through a kaleidoscope   medium   KarmaViz   1.0+  // Kaleidoscope pattern with radial symmetry
vec2 get_pattern(vec2 pos, float t) {
    t = time / 5;
    vec2 center = pos - 0.5;
    float angle = atan(center.y, center.x) * 6.0;
    float dist = length(center);
    return vec2(sin(angle + t) * dist,
                cos(angle - t) * dist) * .1;
}
