KVWM      kaleidoscope_distortion	   geometric@   Kaleidoscope effect with variable segments and radial distortion   medium   KarmaViz Generator   1.0?  // Kaleidoscope0 pattern with radial symmetry
vec2 get_pattern(vec2 pos, float t) {
    t = time * 5;
    vec2 center = pos - sin(t * .00001) - 0.5;
    float angle = atan(center.y, center.x) * 6.0;
    float dist = length(center);
    return vec2(sin(angle - t) * dist,
                atan(angle - t) * dist) * .1;
}
