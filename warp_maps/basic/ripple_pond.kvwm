KVWM      ripple_pond   basic0   Concentric ripples like stones dropped in a pond   medium   KarmaViz Warp Generator   1.0'  // Ripple Pond Warp Map
vec2 get_pattern(vec2 pos, float t) {
    vec2 center = vec2(0.5, 0.5);
    float dist = length(pos - center);
    
    // Multiple ripples with different frequencies
    float ripple1 = sin(dist * 20.0 - t * 3.0) * 0.02;
    float ripple2 = sin(dist * 35.0 - t * 2.0) * 0.01;
    float ripple3 = sin(dist * 50.0 - t * 4.0) * 0.005;
    
    float total_ripple = ripple1 + ripple2 + ripple3;
    
    // Fade out at edges
    total_ripple *= smoothstep(0.7, 0.3, dist);
    
    return normalize(pos - center) * total_ripple;
}