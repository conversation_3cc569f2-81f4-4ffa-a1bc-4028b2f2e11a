KVWM      breathing_space   basic/   Gentle breathing-like expansion and contraction   low   KarmaViz Warp Generator   1.0  // Breathing Space Warp Map
vec2 get_pattern(vec2 pos, float t) {
    vec2 center = vec2(0.5, 0.5);
    vec2 offset = pos - center;
    
    // Breathing pattern
    float breath = sin(t * 0.8) * 0.05;
    
    // Radial expansion/contraction
    return offset * breath;
}