KVWM       Deppth of Field   basicE   Simple blurringt of the edges, simulating photographic depth of field   low   AutoGen   1.0  
// Radial Pulse Warp Map
vec2 get_pattern(vec2 pos, float t) {
    vec2 center = vec2(0.5, 0.5);
    float dist = length(pos - center);
    float pulse = sin(100.0 * dist * t * 2.0) * 0.025 * smoothstep(0.3, 0.5, dist);
    return normalize(pos - center) * pulse;
}
